{"compilerOptions": {"target": "esnext", "jsx": "preserve", "lib": ["esnext", "dom"], "useDefineForClassFields": true, "baseUrl": ".", "module": "esnext", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["@dcloudio/types"], "allowJs": true, "strict": true, "sourceMap": true, "esModuleInterop": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]}