# 今日碎念 - Frontend API Specification

## Overview

This document defines the REST API endpoints that the frontend application expects to consume. The API follows RESTful principles and uses JSON for data exchange.

**Base URL:** `http://localhost:8080/api`
**API Version:** v1
**Content-Type:** `application/json`
**Authentication:** <PERSON><PERSON> (JWT)

## Authentication Flow

### 1. WeChat Mini-Program Login

**Endpoint:** `POST /auth/wechat-login`

**Description:** Authenticate user via WeChat Mini-Program authorization code

**Request Headers:**
```
Content-Type: application/json
X-Client-Version: 1.0.0
X-Platform: wechat-miniprogram
```

**Request Body:**
```json
{
  "code": "string",           // WeChat authorization code (required)
  "nickname": "string",       // User nickname (optional)
  "avatarUrl": "string"       // User avatar URL (optional)
}
```

**Response (200 OK):**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "user": {
      "user_id": "string",
      "open_id": "string",
      "union_id": "string",
      "username": "string",
      "avatar_url": "string",
      "last_login_time": "2024-01-01T00:00:00Z",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    },
    "access_token": "string",
    "refresh_token": "string",
    "expires_in": 7200
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req_123456789"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid authorization code
- `401 Unauthorized`: WeChat authentication failed
- `500 Internal Server Error`: Server error

### 2. Refresh Access Token

**Endpoint:** `POST /auth/refresh-token`

**Request Body:**
```json
{
  "refresh_token": "string"
}
```

**Response:** Same as login response with new tokens

### 3. Logout

**Endpoint:** `POST /auth/logout`

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true
  }
}
```

## User Management

### 1. Get User Profile

**Endpoint:** `GET /user/profile`

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "user_id": "string",
    "open_id": "string",
    "union_id": "string",
    "username": "string",
    "avatar_url": "string",
    "last_login_time": "2024-01-01T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. Update User Profile

**Endpoint:** `PUT /user/profile`

**Request Body:**
```json
{
  "username": "string",       // Optional
  "avatar_url": "string"      // Optional
}
```

**Response:** Same as get profile response

## Emotion Definitions

### 1. Get All Emotions

**Endpoint:** `GET /emotions`

**Response (200 OK):**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "emotion_id": 1,
      "emotion_label": "开心",
      "emotion_icon": "mdi:emoticon-happy-outline",
      "emotion_color": "#FFD93D",
      "is_system": true,
      "user_id": "string",
      "sort_order": 1,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. Create Custom Emotion

**Endpoint:** `POST /emotions`

**Request Body:**
```json
{
  "emotion_label": "string",
  "emotion_icon": "string",
  "emotion_color": "string",
  "sort_order": 1
}
```

### 3. Update Emotion

**Endpoint:** `PUT /emotions/{emotion_id}`

**Request Body:** Same as create emotion

### 4. Delete Emotion

**Endpoint:** `DELETE /emotions/{emotion_id}`

**Response:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true
  }
}
```

## Cause Definitions

### 1. Get All Causes

**Endpoint:** `GET /causes`

**Response (200 OK):**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "cause_id": 1,
      "cause_label": "工作压力",
      "cause_category": "工作",
      "is_system": true,
      "user_id": "string",
      "sort_order": 1,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. Create Custom Cause

**Endpoint:** `POST /causes`

**Request Body:**
```json
{
  "cause_label": "string",
  "cause_category": "string",
  "sort_order": 1
}
```

### 3. Update Cause

**Endpoint:** `PUT /causes/{cause_id}`

### 4. Delete Cause

**Endpoint:** `DELETE /causes/{cause_id}`

## Emotion Entries

### 1. Create Entry

**Endpoint:** `POST /entries`

**Request Body:**
```json
{
  "emotion_id": 1,
  "cause_id": 1,
  "intensity": 4,
  "notes": "string",
  "date": "2024-01-01T00:00:00Z"  // Optional, defaults to current time
}
```

**Response (201 Created):**
```json
{
  "code": 201,
  "message": "success",
  "data": {
    "entry_id": 1,
    "user_id": "string",
    "emotion_id": 1,
    "cause_id": 1,
    "intensity": 4,
    "notes": "string",
    "date": "2024-01-01T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "emotion": {
      // Emotion object
    },
    "cause": {
      // Cause object
    }
  }
}
```

### 2. Query Entries

**Endpoint:** `GET /entries`

**Query Parameters:**
```
page=1                    // Page number (default: 1)
page_size=20             // Items per page (default: 20, max: 100)
emotion_id=1             // Filter by emotion ID
cause_id=1               // Filter by cause ID
start_date=2024-01-01    // Filter by start date (ISO 8601)
end_date=2024-01-31      // Filter by end date (ISO 8601)
min_intensity=1          // Minimum intensity (1-5)
max_intensity=5          // Maximum intensity (1-5)
keyword=search           // Search in notes
sort_by=date             // Sort field (date, intensity, created_at)
sort_order=desc          // Sort direction (asc, desc)
```

**Response (200 OK):**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "entry_id": 1,
        "user_id": "string",
        "emotion_id": 1,
        "cause_id": 1,
        "intensity": 4,
        "notes": "string",
        "date": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "emotion": {
          // Emotion object
        },
        "cause": {
          // Cause object
        }
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5,
    "has_next": true,
    "has_prev": false
  }
}
```

### 3. Get Single Entry

**Endpoint:** `GET /entries/{entry_id}`

### 4. Update Entry

**Endpoint:** `PUT /entries/{entry_id}`

**Request Body:** Same as create entry

### 5. Delete Entry

**Endpoint:** `DELETE /entries/{entry_id}`

## Statistics & Analytics

### 1. Get Emotion Statistics

**Endpoint:** `GET /stats/emotions`

**Query Parameters:**
```
type=emotion             // Statistics type: emotion, cause, intensity
time_range=week          // Time range: day, week, month
start_date=2024-01-01    // Custom start date (optional)
end_date=2024-01-31      // Custom end date (optional)
group_by=day             // Group by: day, week, month, category
```

**Response (200 OK):**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "stats": [
      {
        "emotion": {
          "emotion_id": 1,
          "emotion_label": "开心",
          "emotion_color": "#FFD93D"
        },
        "count": 15,
        "percentage": 30.0,
        "avg_intensity": 4.2
      }
    ],
    "timeline": [
      {
        "timestamp": "2024-01-01T00:00:00Z",
        "entry": {
          // Entry object
        }
      }
    ],
    "summary": {
      "total_entries": 50,
      "avg_intensity": 3.8,
      "most_common_emotion": "开心",
      "most_common_cause": "工作压力",
      "date_range": {
        "start": "2024-01-01T00:00:00Z",
        "end": "2024-01-31T23:59:59Z"
      }
    }
  }
}
```

### 2. Get Cause Statistics

**Endpoint:** `GET /stats/causes`

**Query Parameters:** Same as emotion statistics

**Response:** Similar structure with cause data instead of emotion data

### 3. Get Intensity Statistics

**Endpoint:** `GET /stats/intensity`

**Response:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "intensity_distribution": [
      {
        "intensity": 1,
        "count": 5,
        "percentage": 10.0
      },
      {
        "intensity": 2,
        "count": 8,
        "percentage": 16.0
      }
    ],
    "avg_intensity": 3.2,
    "trend": [
      {
        "date": "2024-01-01",
        "avg_intensity": 3.5
      }
    ]
  }
}
```

## Data Export

### 1. Export Data

**Endpoint:** `POST /export`

**Request Body:**
```json
{
  "format": "json",                    // json, csv, excel
  "data_types": ["entries", "emotions", "causes"],
  "date_range": {
    "start": "2024-01-01T00:00:00Z",
    "end": "2024-01-31T23:59:59Z"
  },
  "include_stats": true
}
```

**Response (202 Accepted):**
```json
{
  "code": 202,
  "message": "Export job started",
  "data": {
    "export_id": "export_123456789",
    "status": "processing",
    "estimated_completion": "2024-01-01T00:05:00Z"
  }
}
```

### 2. Get Export Status

**Endpoint:** `GET /export/{export_id}/status`

**Response (200 OK):**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "status": "completed",           // processing, completed, failed
    "progress": 100,
    "download_url": "https://api.example.com/download/export_123456789",
    "filename": "emotion_data_2024-01-01.json",
    "file_size": 1024000,
    "expires_at": "2024-01-02T00:00:00Z"
  }
}
```

## Error Handling

### Standard Error Response Format

```json
{
  "code": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "emotion_id",
      "message": "Emotion ID is required",
      "code": "REQUIRED_FIELD"
    }
  ],
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req_123456789"
}
```

### HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `202 Accepted`: Request accepted for processing
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required or failed
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation failed
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: Service temporarily unavailable

### Error Codes

- `INVALID_TOKEN`: JWT token is invalid or expired
- `REQUIRED_FIELD`: Required field is missing
- `INVALID_FORMAT`: Field format is invalid
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `DUPLICATE_ENTRY`: Resource already exists
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `VALIDATION_FAILED`: Data validation failed

## Rate Limiting

- **Authentication endpoints**: 10 requests per minute per IP
- **Data modification endpoints**: 100 requests per minute per user
- **Data retrieval endpoints**: 1000 requests per minute per user
- **Export endpoints**: 5 requests per hour per user

Rate limit headers:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Caching

### Client-Side Caching

- **Emotions/Causes**: Cache for 1 hour, validate with `If-Modified-Since`
- **User Profile**: Cache for 30 minutes
- **Entries**: Cache for 5 minutes
- **Statistics**: Cache for 15 minutes

### Cache Headers

```
Cache-Control: public, max-age=3600
ETag: "abc123"
Last-Modified: Wed, 21 Oct 2015 07:28:00 GMT
```

## WebSocket Support (Future Enhancement)

For real-time features, the API may support WebSocket connections:

**Endpoint:** `ws://localhost:8080/ws`

**Authentication:** Send JWT token in connection query parameter

**Message Format:**
```json
{
  "type": "entry_created",
  "data": {
    // Entry object
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**Event Types:**
- `entry_created`: New entry added
- `entry_updated`: Entry modified
- `entry_deleted`: Entry removed
- `sync_status`: Sync operation status update
