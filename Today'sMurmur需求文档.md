# **今日碎语小程序需求文档**

---

**版本历史**

| 版本 | 日期         | 主要变更                                                                                                 | 修订者 |
| ---- | ------------ | -------------------------------------------------------------------------------------------------------- | ------ |
| 1.0  | [初始日期]   | 初始草案                                                                                                 |        |
| 1.1  | 2025-05-27   | 更新前端技术栈为 uni-app, Vite, Vue3, Tailwind CSS。调整UI组件实现描述。                                        |        |
| 1.2  | 2025-05-27   | 根据脚手架特性，统一图标系统为 Iconify，明确 API 自动加载、条件编译样式等特性，并全面审查更新文档。 |        |

---

## **1. 产品愿景与定位**

**核心功能：**

1.  快速情绪记录（情绪图标 + 原因分类 + 文本笔记）。
2.  多维度数据可视化与分析（情绪与原因；日/周/月视图）。
3.  一键微信授权登录（唯一登录方式）。

**设计原则：**

-   **舒缓治愈的视觉语言：** 莫兰迪色系 + 圆角，"呼吸感"留白设计。
-   **零学习成本：** 语义清晰的图标，核心操作用户流程不超过3次点击。
-   **沉浸式体验：** 全屏、无干扰记录模式；支持手势导航（如滑动返回）。

---

## **2. 页面结构与UI细节**

*(所有UI元素的布局和样式将主要通过 TailwindCSS 的原子化类进行实现)*

### **2.1. 记录页 (默认视图 / “首页”)**

**布局：**

```
[顶部栏]
  - 标题: "今日碎语" (字体: 系统默认, 大小: 22px, 字重: Medium/Semibold)
  - 右侧: 用户头像按钮 (点击跳转至个人中心)

[主要操作区]
  - 焦点卡片: "今日最新碎语快照" (使用TailwindCSS构建卡片样式)
    - 若今日已有记录:
      - 大号情绪图标 (来自最新记录, 通过Iconify组件渲染, 图标名称存储于数据库)
      - 情绪标签 (来自最新记录)
      - 情绪强度 (1-5点: ●○○○○ 至 ●●●●●)
      - 最新记录时间戳 (例如："下午 3:15")
      - [可选] 笔记预览 (例如：前20个字符)
    - 若今日无记录:
      - 温馨问候语 (例如："今天感觉怎么样？")
      - [可选] 柔和背景插画/图案
  - 快速记录按钮 (浮动操作按钮, 直径72px, 点击跳转至情绪记录页, 使用Tailwind CSS进行样式定义)

[底部导航栏] (uni-app内置tabbar或使用TailwindCSS自定义组件实现)
  - 记录 (激活状态图标)
  - 图表 (条形图图标)
  - 日记 (笔记本图标)
```

**交互：**

-   点击焦点卡片会打开一个“情绪趋势预览”弹窗。若今日无记录，点击则跳转至情绪记录页。
-   下拉刷新同步数据（显示波纹加载动画，可使用SVG或CSS实现）。

---

### **2.2. 情绪记录页**

**布局：**

```
[头部]
  - 返回按钮 (左上角, Iconify图标)
  - 时间戳 (居中, 例如："下午 2:32 • 今天")

[情绪选择器]
  - 3x3 网格 (间距: 24px, 使用Tailwind CSS grid工具类)
  - 每个情绪单元格:
    • 中心图标 (Iconify图标, 图标名称来自 `Emotion.emotion_icon`, 默认文本颜色, 选中时外圈高亮/脉冲效果，颜色为 `Emotion.emotion_color`; 48x48px视觉容器)
    • 底部标签 (垂直, `Emotion.emotion_label`)

[原因分类区]
  - 水平滚动标签栏 (数据源: DISTINCT `Cause.cause_category`)
    例如："工作" | "社交" | "健康" | "生活" | "其他" (使用TailwindCSS构建自定义滚动组件)
  - 子选项瀑布流 (每行3列, 圆角胶囊按钮; 数据源: `Cause.cause_label`, 根据选中的 `cause_category` 过滤, 使用TailwindCSS构建)

[笔记输入区]
  - 自动调整大小的文本输入框 (最小高度: 120px, uni-app `textarea` 组件, 使用TailwindCSS定义样式)
  - 实时字数统计 (右下角, "0/500")
  - 底部工具栏: Emoji选择器图标 (Iconify) | 语音输入图标 (Iconify, 语音转文字到输入框)

[提交按钮区]
  - 固定底部栏 (带阴影分割线)
  - "保存记录" 按钮 (禁用时透明度50%, 使用TailwindCSS定义样式)
```

**交互：**

-   若必填项（情绪、原因）未选择，提交按钮禁用。
-   输入框背景可根据选中的 `Emotion.emotion_color` 发生细微变化/渐变 (通过动态Tailwind类或内联样式实现)。

---

### **2.3. 图表页 (Dashboard Page)**

*(采用先前讨论的简化版，不依赖ECharts等重型图表库，视觉元素通过自定义组件和TailwindCSS实现)*

**布局：**

```
[时间筛选栏]
  - 日期选择器 (左对齐, 带日历图标 (Iconify), uni-app `picker` 组件或自定义组件)
  - 视图切换: 日 | 周 | 月 (胶囊按钮组 - 使用TailwindCSS自定义组件)

[图表类型切换]
  - 标签页/按钮组: [ 情绪分析 | 原因分析 ] (使用TailwindCSS自定义Tabs组件, 或uni-app `segmented-control` 若适用)

[内容区 - 根据图表类型切换动态显示]

  [[若选中 "情绪分析"]]
    [情绪时间轴概览 (仅日视图)]
      - 视觉表现: 水平时间轴, 使用Iconify情绪图标或彩色圆点标记时间点，圆点大小/图标旁数字表示强度。 (TailwindCSS实现布局和样式)
      - 交互: 点击标记点显示简要信息弹窗。
    [情绪统计列表]
      - 视觉表现: 列表展示各情绪频次，可带简单水平条 (div+背景色+动态宽度) 表示占比。 (TailwindCSS实现)

  [[若选中 "原因分析"]]
    [原因图表子切换]
      - 标签页/按钮组: [ 按类别 | 按具体原因 ] (使用TailwindCSS自定义Tabs组件)
    [原因统计列表]
      - 视觉表现: 列表展示各原因类别/具体原因频次，可带简单水平条。 (TailwindCSS实现)

[洞察总结]
  - 文本卡片 (浅色背景, 使用TailwindCSS定义样式, 内容根据当前分析视图动态生成):
    - "此[时间范围]内最频繁的情绪: [情绪标签] ([计数/百分比]%)"
    - "常见原因类别: [原因类别] ([百分比]%)" (若在原因类别视图)
    - "关键具体原因: [原因标签] ([计数/百分比]%)" (若在具体原因视图)
```

**交互：**

-   当“图表类型切换”或“原因图表子切换”选项改变时，内容区动态更新。
-   点击简易图表中的标记点或列表项，可显示详情弹窗。
-   当无数据时，所有图表区域显示统一的空状态（插画 +引导文字）。

---

### **2.4. 日记页**

**布局：**

```
[筛选栏]
  - 情绪筛选 (下拉菜单/picker, 带颜色指示, 基于 `Emotion` 表)
  - 日期筛选 (YYYY-MM-DD 格式, uni-app `picker` 组件或自定义组件)

[日记条目列表]
  - 卡片式瀑布流 (每行1列, 卡片使用TailwindCSS构建样式)
  - 卡片内容:
    • 头部: 情绪图标 (Iconify, 图标名称来自 `Entry.emotion.iconClass`, 颜色来自 `Entry.emotion.color`) + 情绪强度 (点状表示)
    • 内容预览: "今天的会议压力好大..." (`Entry.notes` 预览, 最多3行)
    • 原因标签 (灰色圆角矩形, `Cause.cause_label`, 使用TailwindCSS定义样式)
    • 时间戳 (例如："下午 2:32", 来自 `Entry.date`)

[空状态]
  - 插画 + 文字: "还没有任何记录"
  - 主要操作按钮: "开始第一条记录" (跳转至情绪记录页)
```

**交互：**

-   点击卡片打开详情弹窗（完整笔记、情绪、原因、时间等）。
-   卡片左滑显示“编辑”和“删除”按钮 (利用uni-app的`uni-swipe-action`组件或自定义实现)。
-   删除操作需要确认对话框（警告红色调，例如：“确定要删除这条记录吗？此操作无法撤销。”）。

---

### **2.5. 个人中心页**

**布局：**

```
[用户信息区]
  - 头像 (圆形, 直径80px, 带边框, `User.avatar_url`, 使用TailwindCSS定义样式)
  - 昵称 (头像下方居中, `User.nickname`)
  - "上次登录: [User.last_login_time]" (小号辅助文字, 格式 YYYY-MM-DD HH:mm)

[功能入口] (列表样式, 每行一项, 左侧图标 (Iconify), 右侧文字, 箭头指示, 使用TailwindCSS构建)
  - 自定义情绪库 (例如 Iconify `fluent:palette-24-regular` 图标)
  - 帮助中心 (例如 Iconify `fluent:question-circle-24-regular` 图标)
  - [可选] 关于我们 (例如 Iconify `fluent:info-24-regular` 图标)
  - [可选] 安全设置 (例如 Iconify `fluent:lock-closed-24-regular` 图标)

[底部区域]
  - 版本号 (页面底部居中或右下角浮动 "版本 X.Y.Z")
  - 退出登录按钮 (红色文字按钮, 底部居中, 明显分隔, 使用TailwindCSS定义样式)
```

**交互：**

-   用户信息区背景可巧妙匹配头像主色调或使用统一浅色背景 (通过TailwindCSS实现)。
-   点击功能入口跳转至相应页面或触发操作，并有点击反馈。
-   点击版本号可触发彩蛋动画（例如：细微的粒子效果）。

---

## **3. 视觉规范**

### **3.1. 色板**

| 类型       | 主要十六进制值 | 使用场景                                   |
| ---------- | -------------- | ------------------------------------------ |
| 主色调     | `#8ECAE6`      | 按钮、主要图表元素                         |
| 情绪色相   | 多种           | 情绪图标背景/强调色 (来自 `Emotion.emotion_color`) |
| 辅助色     | `#F4F1DE`      | 背景、卡片底色                             |
| 警告/危险色 | `#FA5151`      | 删除操作、警告提示                         |
| 文本色     | `#2A2A2A`      | 主要文本内容                               |
| 浅灰文本色 | `#888888`      | 辅助文本、次要信息                         |
| 分割线     | `#EAEAEA`      | 内容分隔                                   |
*(以上色值将配置到 TailwindCSS 的主题中以方便使用)*

### **3.2. 字体排印**

| 场景         | 字体栈 (iOS优先, Android备用)      | 大小 | 字重          |
| ------------ | ---------------------------------- | ---- | ------------- |
| 页面标题     | PingFang SC, Roboto/Noto Sans      | 20px | Bold          |
| 正文/列表文本 | PingFang SC, Roboto/Noto Sans      | 16px | Regular       |
| 辅助文本     | PingFang SC, Roboto/Noto Sans      | 12px | Light         |
| 数据标签     | DIN Alternate (或系统数字优化字体) | 14px | Bold          |
| 首页标题     | 系统默认                           | 22px | Medium/Semibold |
*(注意：优先使用系统原生字体以保证跨平台一致性和性能。TailwindCSS 将配置为使用这些字体栈。)*

### **3.3. 图标系统**

-   **核心方案：Iconify**
    -   所有图标通过 `icones.js.org` 进行查找和筛选，选用合适的开源图标集（如 Fluent, Material Design Icons, Phosphor 等）。
    -   通过与 Vue 3 / uni-app 兼容的 Iconify 组件（例如 `@iconify/vue` 或针对 uni-app 的适配方案）在项目中使用。
-   **情绪图标：**
    -   从 Iconify 中选取，风格力求统一（如 Fluent Design 的线性、柔和风格）。
    -   数据库 `Emotion.emotion_icon` 字段存储 Iconify 图标的完整名称 (例如: `fluent:emoji-happy-24-regular`, `mdi:emoticon-sad-outline`)。
    -   图标颜色通过 CSS 动态设置，可结合 `Emotion.emotion_color`。
    -   视觉尺寸常规约24px，特定高亮场景可适当增大。
-   **功能图标：**
    -   同样从 Iconify 中选取 (例如：设置用 `mdi:cog`, 帮助用 `mdi:help-circle-outline`)。
-   **加载动画：**
    -   三瓣呼吸圆环 (主色调 `#8ECAE6`，带颜色渐隐效果)。此动画通常通过自定义SVG或纯CSS实现，而非直接依赖Iconify。

---

## **4. 导航流程**

```
微信授权登录 → 记录页 (默认视图)
  ├── 点击浮动操作按钮 → 情绪记录页
  ├── 点击底部导航 → 记录页 | 图表页 | 日记页
  └── 点击头像 → 个人中心页

记录页
  ├── 点击焦点卡片 (有记录时) → 情绪趋势预览弹窗
  └── 点击焦点卡片 (无记录时) → 情绪记录页

情绪记录页
  ├── 保存成功 → 返回记录页 (焦点卡片刷新)
  └── 点击原因分类 (父级) -> 更新子选项瀑布流

图表页
  ├── 筛选变更 / 图表切换 → 图表局部刷新
  └── 点击图表详情 (例如气泡/列表项) → 显示原始记录摘要的弹窗

日记页
  └── 点击列表项 → 详情弹窗
  └── 左滑操作 → 编辑/删除选项

个人中心页
  └── 点击功能入口 → 相应页面/操作
```

---

## **5. 数据模型**

### **5.1. 用户表 (User Table)**

| 字段                                                         | 类型                  | 描述                                                                 |
| ------------------------------------------------------------ | --------------------- | -------------------------------------------------------------------- |
| user_id                                                      | VARCHAR(128)          | 主键, 内部用户唯一标识符 (例如：后端生成的UUID)。                          |
| open_id                                                      | VARCHAR(128)          | 此小程序唯一的微信OpenID。 (索引)                                      |
| union_id                                                     | VARCHAR(128) NULL     | 微信UnionID (若适用, 用于跨多个微信应用/平台关联)。 (索引, 可为空)        |
| username                                                     | VARCHAR(50)           | 用户选择的显示名称或初始微信昵称 (用户可编辑)。                             |
| avatar_url                                                   | VARCHAR(255) NULL     | 用户选择的头像URL或初始微信头像URL (可编辑, 可为空)。                         |
| session_key                                                  | VARCHAR(255)          | 微信Session Key (后端加密存储, 不暴露给前端)。                              |
| last_login_time                                              | TIMESTAMP             | 上次登录时间戳。                                                         |
| created_at                                                   | TIMESTAMP             | 账户创建时间戳 (默认为 CURRENT_TIMESTAMP)。                               |
| updated_at                                                   | TIMESTAMP             | 最后更新时间戳 (默认为 CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)。         |
| *一个特定的 user_id (例如："0" 或 "system_user") 将代表系统用户。* |                       |                                                                      |
| *注意: open_id 是此特定应用与微信身份的主要关联。user_id 是内部ID。* |                       |                                                                      |

### **5.2. 日记条目表 (Entry Table - 情绪日记条目)**

| 字段       | 类型                  | 描述                                                                 |
| ---------- | --------------------- | -------------------------------------------------------------------- |
| entry_id   | BIGINT AUTO_INCREMENT | 主键                                                                   |
| user_id    | VARCHAR(128)          | 外键, 引用 User 表                                                    |
| date       | TIMESTAMP             | 记录时间戳 (精确到秒, 前端提供, 后端校验)                                  |
| notes      | TEXT                  | 日记内容 (静态加密/传输中加密)                                             |
| cause_id   | INT                   | 外键, 引用 Cause 表 (单选, 必填)                                       |
| emotion_id | INT                   | 外键, 引用 Emotion 表 (单选, 必填)                                      |
| intensity  | TINYINT               | 情绪强度 (1-5, 必填)                                                   |
| created_at | TIMESTAMP             | 记录创建时间戳 (默认为 CURRENT_TIMESTAMP)                                  |
| updated_at | TIMESTAMP             | 最后更新时间戳 (默认为 CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)        |

### **5.3. 情绪表 (Emotion Table - 情绪定义)**

| 字段          | 类型               | 描述                                                                 |
| ------------- | ------------------ | -------------------------------------------------------------------- |
| emotion_id    | INT AUTO_INCREMENT | 主键                                                                   |
| emotion_label | VARCHAR(50)        | 情绪标签 (例如："焦虑", 唯一)                                              |
| emotion_icon  | VARCHAR(100)       | Iconify 图标名称 (例如："fluent:emoji-anxious-24-regular", "mdi:emoticon-neutral-outline") |
| emotion_color | VARCHAR(20)        | HEX颜色代码 (例如："#FF6B6B") 用于IconFont颜色及相关UI元素                 |
| is_system     | BOOLEAN            | 若为系统定义则为True, 用户定义则为False                                   |
| user_id       | VARCHAR(128)       | 外键, 引用 User 表 (系统情绪关联到系统用户ID, 自定义情绪关联到实际用户ID) |
| sort_order    | INT                | [可选] 网格/列表的显示顺序                                                |
| created_at    | TIMESTAMP          | 创建时间戳 (默认为 CURRENT_TIMESTAMP)                                      |
| updated_at    | TIMESTAMP          | 最后更新时间戳 (默认为 CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)        |

### **5.4. 原因表 (Cause Table - 原因定义)**

| 字段           | 类型               | 描述                                                                 |
| -------------- | ------------------ | -------------------------------------------------------------------- |
| cause_id       | INT AUTO_INCREMENT | 主键                                                                   |
| cause_label    | VARCHAR(50)        | 原因标签 (例如："工作压力", 在一个分类内或用户级别唯一)                       |
| cause_category | VARCHAR(50)        | 类别标签 (例如："工作", 系统定义或用户可自定义)                              |
| is_system      | BOOLEAN            | 若为系统定义则为True, 用户定义则为False                                   |
| user_id        | VARCHAR(128)       | 外键, 引用 User 表 (逻辑同 `Emotion.user_id`)                        |
| sort_order     | INT                | [可选] 显示顺序                                                         |
| created_at     | TIMESTAMP          | 创建时间戳 (默认为 CURRENT_TIMESTAMP)                                      |
| updated_at     | TIMESTAMP          | 最后更新时间戳 (默认为 CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)        |

---

## **6. 技术栈与实现说明**

-   **前端框架：** **uni-app** (使用 Vue3 语法)
-   **编程语言：** **Vue3 + TypeScript**
-   **构建工具：** **Vite** (配合 **pnpm** 包管理器)
-   **CSS方案：** **TailwindCSS**
    -   采用原子化/功能类优先的样式构建方式。
    -   利用 **uni-app 条件编译样式**特性，灵活处理多端 TailwindCSS 样式的差异性。
-   **图标系统：** **Iconify**
    -   图标通过 `icones.js.org` 查找，使用 `@iconify/vue` 或类似 uni-app 兼容方案集成。
    -   数据库中存储 Iconify 图标名称。
-   **UI组件：**
    -   主要依赖 **uni-app 内置组件**。
    -   复杂或特定UI通过 **自定义Vue3组件** 实现，并使用 **TailwindCSS** 进行样式化。
-   **状态管理：** **Pinia** (推荐，与Vue3生态良好集成)。
-   **API自动加载：** 脚手架支持 Composition API 的自动引入，简化开发。
-   **开发环境与代码质量：**
    -   基于项目模板提供的 **VSCode 配置**。
    -   集成 **ESLint** (代码规范检查), **Stylelint** (样式规范检查), **Prettier** (代码格式化)，确保代码风格统一和质量。
-   **数据存储与同步：** 后端API + **MySQL** 关系型数据库。
-   **系统用户：** 一个专用的 `user_id` (例如："0" 或 "system_user") 将用于标识预定义情绪和原因的系统级记录。
-   **加密：** 敏感用户数据（例如：session_key）在后端加密。日记笔记应在传输和静态存储时加密。

---

## **7. 非功能性需求 (初步)**

-   **性能：**
    -   页面加载时间目标在3秒以内。
    -   UI交互应流畅且响应迅速，Vite 和 uni-app 的优化有助于此目标。
-   **安全：**
    -   用户数据（尤其是日记条目和微信授权详情）必须得到安全处理，防止未经授权的访问或泄露。
    -   遵循最小权限原则。
-   **兼容性：**
    -   **主要目标平台：微信小程序。**
    -   利用 uni-app 的跨端能力，为未来潜在扩展到其他平台 (H5, App等) 保留可能性。
    -   兼容主流的iOS和Android系统版本 (由uni-app框架负责底层兼容性)。
-   **易用性：**
    -   遵守微信小程序设计指南，确保界面直观、操作便捷。
    -   提供清晰的用户引导和反馈。
-   **可维护性：**
    -   代码结构清晰，模块化设计。
    -   遵循约定的代码规范 (ESLint, Stylelint)。

---