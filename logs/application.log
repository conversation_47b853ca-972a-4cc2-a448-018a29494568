2025-05-27 15:27:30.692 [main] INFO  c.t.api.TodayMurmurApplication - Starting TodayMurmurApplication using Java 11.0.27 on MacBook-Air.local with PID 80693 (/Users/<USER>/Desktop/work-NJ/SELF/今日碎念/codes/backend/target/classes started by xinpengguo in /Users/<USER>/Desktop/work-NJ/SELF/今日碎念/codes/backend)
2025-05-27 15:27:30.696 [main] DEBUG c.t.api.TodayMurmurApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-05-27 15:27:30.699 [main] INFO  c.t.api.TodayMurmurApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-27 15:27:31.654 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-27 15:27:31.655 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27 15:27:31.718 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 59 ms. Found 4 JPA repository interfaces.
2025-05-27 15:27:31.736 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-27 15:27:31.737 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-27 15:27:31.751 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.todaymurmur.api.repository.CauseRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-27 15:27:31.751 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.todaymurmur.api.repository.EmotionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-27 15:27:31.752 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.todaymurmur.api.repository.EntryRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-27 15:27:31.752 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.todaymurmur.api.repository.UserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-05-27 15:27:31.752 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-05-27 15:27:32.488 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-05-27 15:27:32.494 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-27 15:27:32.494 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-27 15:27:32.610 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-05-27 15:27:32.611 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1809 ms
2025-05-27 15:27:32.829 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-27 15:27:32.923 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-27 15:27:33.761 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27 15:27:34.076 [main] DEBUG c.t.a.s.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-05-27 15:27:34.565 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27 15:27:34.663 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/auth/**']
2025-05-27 15:27:34.666 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/wechat/**']
2025-05-27 15:27:34.666 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/h2-console/**']
2025-05-27 15:27:34.666 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/health']
2025-05-27 15:27:34.666 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for Ant [pattern='/health/auth']
2025-05-27 15:27:34.666 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-05-27 15:27:34.670 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2220c5f7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5bb90b89, org.springframework.security.web.context.SecurityContextPersistenceFilter@5263f554, org.springframework.security.web.header.HeaderWriterFilter@7dbe858f, org.springframework.web.filter.CorsFilter@10b0dd3a, org.springframework.security.web.authentication.logout.LogoutFilter@69f55ea, com.todaymurmur.api.security.JwtAuthenticationFilter@7f584d0c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@95958d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@70fe33fa, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4c231f62, org.springframework.security.web.session.SessionManagementFilter@285ac29, org.springframework.security.web.access.ExceptionTranslationFilter@643fed50, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@397f9672]
2025-05-27 15:27:34.973 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-05-27 15:27:34.993 [main] INFO  c.t.api.TodayMurmurApplication - Started TodayMurmurApplication in 4.807 seconds (JVM running for 5.718)
2025-05-27 16:23:54.739 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6m38s354ms).
2025-05-27 16:24:36.483 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27 16:24:36.490 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-27 16:24:36.503 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
