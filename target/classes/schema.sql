CREATE TABLE `user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT,
  `avatar_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `nickname` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `open_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `session_key` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `union_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `username` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_login_time` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `UK_4q41dbud42509bjgnmvd7n2ro` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cause` (
  `cause_id` bigint NOT NULL AUTO_INCREMENT,
  `cause_label` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `category_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_custom` bit(1) NOT NULL,
  `user_id` bigint DEFAULT -1 NULL,
  `is_deleted` bit(1) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  PRIMARY KEY (`cause_id`),
  UNIQUE KEY `idx_user_cause` (`user_id`,`cause_label`),
  CONSTRAINT `FKf9jfxg95g636xhpy76psirrg7` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `emotion` (
  `emotion_id` bigint NOT NULL AUTO_INCREMENT,
  `emotion_label` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `emotion_color` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
  `emotion_icon` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
  `is_custom` bit(1) NOT NULL,
  `user_id` bigint DEFAULT -1 NULL,
  `is_deleted` bit(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`emotion_id`),
  UNIQUE KEY `idx_user_emotion` (`user_id`,`emotion_label`),
  CONSTRAINT `FK13e10yt0kc0n531k2x1ut0n05` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `entry` (
  `entry_id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `cause_id` bigint NOT NULL,
  `emotion_id` bigint NOT NULL,
  `date` datetime(6) NOT NULL,
  `intensity` int DEFAULT NULL,
  `notes` text COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`entry_id`),
  KEY `idx_user_date` (`user_id`,`date`),
  CONSTRAINT `FKcjo0axu4oh5otcjcun7o190lx` FOREIGN KEY (`emotion_id`) REFERENCES `emotion` (`emotion_id`),
  CONSTRAINT `FKoia5s1p9sk4x5fld87yjqpjg9` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`),
  CONSTRAINT `FKqnxh6ujbkgxlo4tjvkfnxjje4` FOREIGN KEY (`cause_id`) REFERENCES `cause` (`cause_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
