import * as Pinia from 'pinia'
import { createSSRApp } from 'vue'
import App from './App.vue'
import { initStores } from './stores'

export function createApp() {
  const app = createSSRApp(App)
  app.use(Pinia.createPinia())

  // 异步初始化所有 stores
  initStores({
    useEnhanced: true, // 使用增强版 stores
    enableMigration: true,
    enableAutoSync: true,
  }).catch((error) => {
    // eslint-disable-next-line no-console
    console.error('Store 初始化失败:', error)
  })

  return {
    app,
    Pinia,
  }
}
