/**
 * Store 组合式工具函数
 * 提供高级状态管理模式和工具函数
 */

import type { Ref } from 'vue'
import type {
  ApiResponse,
  AppError,
  CacheConfig,
  CacheItem,
  ErrorType,
  SyncQueueItem,
  SyncStatus,
} from '@/types'
import { computed, ref, watch } from 'vue'

/**
 * 异步状态管理
 */
export interface AsyncState<T> {
  /** 数据 */
  data: Ref<T | null>
  /** 加载状态 */
  loading: Ref<boolean>
  /** 错误信息 */
  error: Ref<AppError | null>
  /** 是否已加载 */
  loaded: Ref<boolean>
  /** 重新加载 */
  reload: () => Promise<void>
  /** 清除错误 */
  clearError: () => void
  /** 重置状态 */
  reset: () => void
}

/**
 * 创建异步状态
 */
export function useAsyncState<T>(
  asyncFn: () => Promise<T>,
  initialData: T | null = null,
  options: {
    immediate?: boolean
    resetOnExecute?: boolean
    onError?: (error: AppError) => void
    onSuccess?: (data: T) => void
  } = {},
): AsyncState<T> {
  const data = ref<T | null>(initialData)
  const loading = ref(false)
  const error = ref<AppError | null>(null)
  const loaded = ref(false)

  const execute = async () => {
    if (options.resetOnExecute) {
      data.value = null
    }

    loading.value = true
    error.value = null

    try {
      const result = await asyncFn()
      data.value = result
      loaded.value = true

      if (options.onSuccess) {
        options.onSuccess(result)
      }
    }
    catch (err) {
      const appError = err as AppError
      error.value = appError

      if (options.onError) {
        options.onError(appError)
      }
    }
    finally {
      loading.value = false
    }
  }

  const reload = async () => {
    await execute()
  }

  const clearError = () => {
    error.value = null
  }

  const reset = () => {
    data.value = initialData
    loading.value = false
    error.value = null
    loaded.value = false
  }

  // 立即执行
  if (options.immediate !== false) {
    execute()
  }

  return {
    data,
    loading,
    error,
    loaded,
    reload,
    clearError,
    reset,
  }
}

/**
 * 乐观更新状态管理
 */
export interface OptimisticState<T> {
  /** 当前数据 */
  data: Ref<T>
  /** 是否有待处理的更新 */
  pending: Ref<boolean>
  /** 乐观更新 */
  optimisticUpdate: (updater: (data: T) => T, asyncFn: () => Promise<T>) => Promise<void>
  /** 回滚更新 */
  rollback: () => void
}

/**
 * 创建乐观更新状态
 */
export function useOptimisticState<T>(initialData: T): OptimisticState<T> {
  const data = ref<T>(initialData)
  const pending = ref(false)
  const previousData = ref<T>(initialData)

  const optimisticUpdate = async (
    updater: (data: T) => T,
    asyncFn: () => Promise<T>,
  ) => {
    // 保存当前数据
    previousData.value = JSON.parse(JSON.stringify(data.value))

    // 立即应用乐观更新
    data.value = updater(data.value)
    pending.value = true

    try {
      // 执行异步操作
      const result = await asyncFn()
      data.value = result
    }
    catch (error) {
      // 失败时回滚
      data.value = previousData.value
      throw error
    }
    finally {
      pending.value = false
    }
  }

  const rollback = () => {
    data.value = previousData.value
    pending.value = false
  }

  return {
    data,
    pending,
    optimisticUpdate,
    rollback,
  }
}

/**
 * 缓存管理
 */
export interface CacheManager<T> {
  /** 获取缓存 */
  get: (key: string) => T | null
  /** 设置缓存 */
  set: (key: string, value: T, ttl?: number) => void
  /** 删除缓存 */
  delete: (key: string) => boolean
  /** 清空缓存 */
  clear: () => void
  /** 检查是否存在 */
  has: (key: string) => boolean
  /** 获取缓存大小 */
  size: () => number
}

/**
 * 创建缓存管理器
 */
export function useCacheManager<T>(config: CacheConfig): CacheManager<T> {
  const cache = new Map<string, CacheItem<T>>()

  const cleanup = () => {
    const now = Date.now()
    const entries = Array.from(cache.entries())

    // 删除过期项
    entries.forEach(([key, item]) => {
      if (item.expires_at < now) {
        cache.delete(key)
      }
    })

    // 如果超过最大大小，根据策略清理
    if (cache.size > config.max_size) {
      const sortedEntries = entries
        .filter(([, item]) => item.expires_at >= now)
        .sort((a, b) => {
          switch (config.cleanup_strategy) {
            case 'lru':
              return a[1].created_at - b[1].created_at
            case 'fifo':
              return a[1].created_at - b[1].created_at
            case 'ttl':
              return a[1].expires_at - b[1].expires_at
            default:
              return 0
          }
        })

      const toDelete = sortedEntries.slice(0, cache.size - config.max_size)
      toDelete.forEach(([key]) => cache.delete(key))
    }
  }

  const get = (key: string): T | null => {
    const item = cache.get(key)
    if (!item) { return null }

    if (item.expires_at < Date.now()) {
      cache.delete(key)
      return null
    }

    return item.data
  }

  const set = (key: string, value: T, ttl?: number): void => {
    const now = Date.now()
    const item: CacheItem<T> = {
      key,
      data: value,
      created_at: now,
      expires_at: now + (ttl || config.default_ttl),
      version: 1,
    }

    cache.set(key, item)
    cleanup()
  }

  const deleteItem = (key: string): boolean => {
    return cache.delete(key)
  }

  const clear = (): void => {
    cache.clear()
  }

  const has = (key: string): boolean => {
    const item = cache.get(key)
    return item !== undefined && item.expires_at >= Date.now()
  }

  const size = (): number => {
    cleanup()
    return cache.size
  }

  return {
    get,
    set,
    delete: deleteItem,
    clear,
    has,
    size,
  }
}

/**
 * 数据同步管理
 */
export interface SyncManager {
  /** 同步状态 */
  status: Ref<SyncStatus>
  /** 同步队列 */
  queue: Ref<SyncQueueItem[]>
  /** 添加到同步队列 */
  enqueue: (item: Omit<SyncQueueItem, 'id' | 'created_at' | 'retry_count'>) => void
  /** 开始同步 */
  sync: () => Promise<void>
  /** 清空队列 */
  clearQueue: () => void
  /** 重试失败项 */
  retryFailed: () => Promise<void>
}

/**
 * 创建同步管理器
 */
export function useSyncManager(
  syncHandler: (item: SyncQueueItem) => Promise<void>,
): SyncManager {
  const status = ref<SyncStatus>({
    status: 'idle',
    last_sync: '',
    pending_count: 0,
    error_message: undefined,
  })

  const queue = ref<SyncQueueItem[]>([])

  const enqueue = (item: Omit<SyncQueueItem, 'id' | 'created_at' | 'retry_count'>) => {
    const queueItem: SyncQueueItem = {
      ...item,
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      created_at: new Date().toISOString(),
      retry_count: 0,
    }

    queue.value.push(queueItem)
    status.value.pending_count = queue.value.length
  }

  const sync = async () => {
    if (status.value.status === 'syncing' || queue.value.length === 0) {
      return
    }

    status.value.status = 'syncing'
    status.value.error_message = undefined

    const itemsToSync = [...queue.value]
    const failedItems: SyncQueueItem[] = []

    for (const item of itemsToSync) {
      try {
        await syncHandler(item)
        // 成功后从队列中移除
        const index = queue.value.findIndex(q => q.id === item.id)
        if (index !== -1) {
          queue.value.splice(index, 1)
        }
      }
      catch (error) {
        // 失败时增加重试次数
        item.retry_count++
        if (item.retry_count < item.max_retries) {
          failedItems.push(item)
        }
        else {
          // 超过最大重试次数，从队列中移除
          const index = queue.value.findIndex(q => q.id === item.id)
          if (index !== -1) {
            queue.value.splice(index, 1)
          }
        }
      }
    }

    // 更新状态
    status.value.pending_count = queue.value.length
    status.value.last_sync = new Date().toISOString()

    if (failedItems.length > 0) {
      status.value.status = 'error'
      status.value.error_message = `${failedItems.length} 项同步失败`
    }
    else {
      status.value.status = 'success'
    }
  }

  const clearQueue = () => {
    queue.value = []
    status.value.pending_count = 0
  }

  const retryFailed = async () => {
    const failedItems = queue.value.filter(item => item.retry_count > 0)
    if (failedItems.length > 0) {
      await sync()
    }
  }

  return {
    status,
    queue,
    enqueue,
    sync,
    clearQueue,
    retryFailed,
  }
}

/**
 * 重试机制
 */
export interface RetryOptions {
  /** 最大重试次数 */
  maxRetries: number
  /** 重试延迟 (毫秒) */
  delay: number
  /** 退避因子 */
  backoffFactor: number
  /** 重试条件 */
  shouldRetry?: (error: any) => boolean
}

/**
 * 带重试的异步函数执行
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions,
): Promise<T> {
  let lastError: any
  let delay = options.delay

  for (let attempt = 0; attempt <= options.maxRetries; attempt++) {
    try {
      return await fn()
    }
    catch (error) {
      lastError = error

      // 检查是否应该重试
      if (
        attempt === options.maxRetries
        || (options.shouldRetry && !options.shouldRetry(error))
      ) {
        break
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay))
      delay *= options.backoffFactor
    }
  }

  throw lastError
}

/**
 * 防抖函数
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
): T {
  let timeoutId: number | undefined

  return ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      fn(...args)
    }, delay) as unknown as number
  }) as T
}

/**
 * 节流函数
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
): T {
  let lastCall = 0

  return ((...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return fn(...args)
    }
  }) as T
}
