<script setup lang="ts">
import { computed } from 'vue'
import { usePageAuthGuard } from '@/composables/useAuthGuard'
import { useEmotionsStore } from '@/stores'
import { useUserStore } from '@/stores/user'

// 认证守卫
const authGuard = usePageAuthGuard()

// Store
const userStore = useUserStore()
const emotionsStore = useEmotionsStore()

// 计算属性
const todayLatestEntry = computed(() => emotionsStore.todayLatestEntry)

// 方法
function goToProfile() {
  uni.navigateTo({
    url: '/pages/profile/index',
  })
}

function goToEmotionRecord() {
  uni.navigateTo({
    url: '/pages/emotion-record/index',
  })
}

function handleFocusCardClick() {
  if (todayLatestEntry.value) {
    // 有记录时显示趋势预览弹窗 (暂时跳转到图表页)
    uni.switchTab({
      url: '/pages/charts/index',
    })
  }
  else {
    // 无记录时跳转到情绪记录页
    goToEmotionRecord()
  }
}

function getEmotionIcon(iconName?: string) {
  // 简化的图标映射，实际应该使用 Iconify
  const iconMap: Record<string, string> = {
    'mdi:emoticon-happy-outline': '😊',
    'mdi:emoticon-neutral-outline': '😐',
    'mdi:emoticon-sad-outline': '😢',
    'mdi:emoticon-confused-outline': '😕',
    'mdi:emoticon-angry-outline': '😠',
    'mdi:emoticon-excited-outline': '🤩',
    'mdi:emoticon-dead-outline': '😴',
    'mdi:heart-outline': '💖',
    'mdi:help-circle-outline': '🤔',
  }
  return iconMap[iconName || ''] || '😊'
}

function formatTime(dateString: string) {
  const date = new Date(dateString)
  const hours = date.getHours()
  const minutes = date.getMinutes()
  const period = hours >= 12 ? '下午' : '上午'
  const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours
  return `${period} ${displayHours}:${minutes.toString().padStart(2, '0')}`
}

// 页面生命周期
onShow(() => {
  // 页面显示时刷新数据
  emotionsStore.init()
})
</script>

<template>
  <view class="record-page">
    <!-- 顶部栏 -->
    <view class="header">
      <text class="title">今日碎语</text>
      <view class="avatar-btn" @click="goToProfile">
        <view class="avatar">
          <image
            v-if="userStore.userAvatar"
            :src="userStore.userAvatar"
            class="avatar-image"
            mode="aspectFill"
          />
          <text v-else class="avatar-text">{{ userStore.userName?.charAt(0) || '👤' }}</text>
        </view>
      </view>
    </view>

    <!-- 主要操作区 -->
    <view class="main-content">
      <!-- 焦点卡片 -->
      <view class="focus-card" @click="handleFocusCardClick">
        <view v-if="todayLatestEntry" class="has-record">
          <!-- 有记录时显示最新记录信息 -->
          <view class="emotion-display">
            <view
              class="emotion-icon"
              :style="{ color: todayLatestEntry.emotion?.emotion_color }"
            >
              <text class="icon">{{ getEmotionIcon(todayLatestEntry.emotion?.emotion_icon) }}</text>
            </view>
            <text class="emotion-label">{{ todayLatestEntry.emotion?.emotion_label }}</text>
          </view>

          <view class="intensity-display">
            <view class="intensity-dots">
              <view
                v-for="i in 5"
                :key="i"
                class="dot"
                :class="{ active: i <= todayLatestEntry.intensity }"
              />
            </view>
          </view>

          <view class="record-info">
            <text class="time">{{ formatTime(todayLatestEntry.date) }}</text>
            <text v-if="todayLatestEntry.notes" class="notes-preview">
              {{ todayLatestEntry.notes.substring(0, 20) }}{{ todayLatestEntry.notes.length > 20 ? '...' : '' }}
            </text>
          </view>
        </view>

        <view v-else class="no-record">
          <!-- 无记录时显示欢迎引导 -->
          <view class="welcome-icon">
            <text class="icon">😊</text>
          </view>
          <text class="welcome-text">今天感觉怎么样？</text>
          <text class="welcome-subtitle">点击开始记录你的情绪</text>
        </view>
      </view>

      <!-- 快速记录按钮 -->
      <view class="fab" @click="goToEmotionRecord">
        <text class="fab-icon">+</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.record-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f4f1de 0%, #e9c46a 100%);
  padding: 0 32rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 0 60rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 600;
  color: #2a2a2a;
}

.avatar-btn {
  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: #8ecae6;
    display: flex;
    align-items: center;
    justify-content: center;

    .avatar-image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }

    .avatar-text {
      color: white;
      font-size: 32rpx;
      font-weight: 600;
    }
  }
}

.main-content {
  position: relative;
  flex: 1;
}

.focus-card {
  background: white;
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 120rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  min-height: 400rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.has-record {
  text-align: center;

  .emotion-display {
    margin-bottom: 40rpx;

    .emotion-icon {
      .icon {
        font-size: 120rpx;
        line-height: 1;
      }
    }

    .emotion-label {
      display: block;
      margin-top: 20rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #2a2a2a;
    }
  }

  .intensity-display {
    margin-bottom: 40rpx;

    .intensity-dots {
      display: flex;
      justify-content: center;
      gap: 16rpx;

      .dot {
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background: #eaeaea;

        &.active {
          background: #8ecae6;
        }
      }
    }
  }

  .record-info {
    .time {
      display: block;
      font-size: 28rpx;
      color: #888888;
      margin-bottom: 16rpx;
    }

    .notes-preview {
      display: block;
      font-size: 24rpx;
      color: #666666;
      line-height: 1.4;
    }
  }
}

.no-record {
  text-align: center;

  .welcome-icon {
    margin-bottom: 40rpx;

    .icon {
      font-size: 120rpx;
      line-height: 1;
    }
  }

  .welcome-text {
    display: block;
    font-size: 36rpx;
    font-weight: 500;
    color: #2a2a2a;
    margin-bottom: 20rpx;
  }

  .welcome-subtitle {
    display: block;
    font-size: 28rpx;
    color: #888888;
  }
}

.fab {
  position: fixed;
  right: 60rpx;
  bottom: 200rpx;
  width: 144rpx;
  height: 144rpx;
  border-radius: 50%;
  background: #8ecae6;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(142, 202, 230, 0.4);

  .fab-icon {
    color: white;
    font-size: 60rpx;
    font-weight: 300;
    line-height: 1;
  }
}
</style>
