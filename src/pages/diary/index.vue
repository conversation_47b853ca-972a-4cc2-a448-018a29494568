<script setup lang="ts">
import type { Entry } from '@/types'
import { computed, ref } from 'vue'
import { useEmotionsStore } from '@/stores'

// Store
const emotionsStore = useEmotionsStore()

// 响应式数据
const selectedEmotionId = ref<number | null>(null)
const selectedDate = ref<string>('')
const showEmotionSelector = ref(false)
const selectedEntry = ref<Entry | null>(null)

// 计算属性
const hasEntries = computed(() => emotionsStore.entries.length > 0)

const selectedEmotionText = computed(() => {
  if (selectedEmotionId.value === null) { return '全部情绪' }
  const emotion = emotionsStore.emotions.find(e => e.emotion_id === selectedEmotionId.value)
  return emotion?.emotion_label || '全部情绪'
})

const selectedDateText = computed(() => {
  if (!selectedDate.value) { return '全部日期' }
  const date = new Date(selectedDate.value)
  return `${date.getMonth() + 1}月${date.getDate()}日`
})

const filteredEntries = computed(() => {
  let entries = [...emotionsStore.entries]

  // 按情绪筛选
  if (selectedEmotionId.value !== null) {
    entries = entries.filter(entry => entry.emotion_id === selectedEmotionId.value)
  }

  // 按日期筛选
  if (selectedDate.value) {
    const targetDate = new Date(selectedDate.value).toDateString()
    entries = entries.filter(entry => new Date(entry.date).toDateString() === targetDate)
  }

  // 按时间倒序排列
  return entries.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
})

// 方法
function showEmotionPicker() {
  showEmotionSelector.value = true
}

function hideEmotionPicker() {
  showEmotionSelector.value = false
}

function selectEmotion(emotionId: number | null) {
  selectedEmotionId.value = emotionId
  hideEmotionPicker()
}

function showDatePicker() {
  uni.showModal({
    title: '提示',
    content: '日期筛选功能开发中',
    showCancel: false,
  })
}

function showEntryDetail(entry: Entry) {
  selectedEntry.value = entry
}

function hideEntryDetail() {
  selectedEntry.value = null
}

function showEntryActions(entry: Entry) {
  uni.showActionSheet({
    itemList: ['查看详情', '编辑', '删除'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          showEntryDetail(entry)
          break
        case 1:
          editEntry(entry)
          break
        case 2:
          confirmDeleteEntry(entry)
          break
      }
    },
  })
}

function editEntry(entry?: Entry) {
  const targetEntry = entry || selectedEntry.value
  if (targetEntry) {
    // 跳转到编辑页面 (暂时显示提示)
    uni.showToast({
      title: '编辑功能开发中',
      icon: 'none',
    })
    hideEntryDetail()
  }
}

function confirmDeleteEntry(entry?: Entry) {
  const targetEntry = entry || selectedEntry.value
  if (targetEntry) {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这条记录吗？此操作无法撤销。',
      confirmColor: '#FA5151',
      success: (res) => {
        if (res.confirm) {
          emotionsStore.deleteEntry(targetEntry.entry_id)
          hideEntryDetail()
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
        }
      },
    })
  }
}

function goToEmotionRecord() {
  uni.navigateTo({
    url: '/pages/emotion-record/index',
  })
}

function getEmotionIcon(iconName?: string) {
  const iconMap: Record<string, string> = {
    'mdi:emoticon-happy-outline': '😊',
    'mdi:emoticon-neutral-outline': '😐',
    'mdi:emoticon-sad-outline': '😢',
    'mdi:emoticon-confused-outline': '😕',
    'mdi:emoticon-angry-outline': '😠',
    'mdi:emoticon-excited-outline': '🤩',
    'mdi:emoticon-dead-outline': '😴',
    'mdi:heart-outline': '💖',
    'mdi:help-circle-outline': '🤔',
  }
  return iconMap[iconName || ''] || '😊'
}

function formatEntryTime(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    const hours = date.getHours()
    const minutes = date.getMinutes()
    const period = hours >= 12 ? '下午' : '上午'
    const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours
    return `今天 ${period} ${displayHours}:${minutes.toString().padStart(2, '0')}`
  }
  else if (diffDays === 1) {
    return '昨天'
  }
  else if (diffDays < 7) {
    return `${diffDays}天前`
  }
  else {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }
}

function formatDetailTime(dateString: string) {
  const date = new Date(dateString)
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 页面生命周期
onShow(() => {
  console.log('日记页面显示')
})
</script>

<template>
  <view class="diary-page">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="emotion-filter" @click="showEmotionPicker">
        <text class="filter-text">{{ selectedEmotionText }}</text>
        <text class="filter-icon">▼</text>
      </view>

      <view class="date-filter" @click="showDatePicker">
        <text class="filter-text">{{ selectedDateText }}</text>
        <text class="filter-icon">📅</text>
      </view>
    </view>

    <!-- 日记条目列表 -->
    <view class="entries-list">
      <view
        v-for="entry in filteredEntries"
        :key="entry.entry_id"
        class="entry-card"
        @click="showEntryDetail(entry)"
        @longpress="showEntryActions(entry)"
      >
        <!-- 头部：情绪图标 + 强度 -->
        <view class="entry-header">
          <view class="emotion-info">
            <view
              class="emotion-icon"
              :style="{ color: entry.emotion?.emotion_color }"
            >
              <text class="icon">{{ getEmotionIcon(entry.emotion?.emotion_icon) }}</text>
            </view>
            <view class="intensity-dots">
              <view
                v-for="i in 5"
                :key="i"
                class="dot"
                :class="{ active: i <= entry.intensity }"
              />
            </view>
          </view>
          <view class="entry-time">
            <text>{{ formatEntryTime(entry.date) }}</text>
          </view>
        </view>

        <!-- 内容预览 -->
        <view class="entry-content">
          <text class="notes-preview">{{ entry.notes || '无笔记内容' }}</text>
        </view>

        <!-- 原因标签 -->
        <view class="entry-footer">
          <view class="cause-tag">
            <text>{{ entry.cause?.cause_label }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="filteredEntries.length === 0" class="empty-state">
      <view class="empty-icon">
        📖
      </view>
      <text class="empty-text">{{ hasEntries ? '没有符合条件的记录' : '还没有任何记录' }}</text>
      <text class="empty-subtitle">
        {{ hasEntries ? '尝试调整筛选条件' : '开始第一条记录' }}
      </text>
      <view v-if="!hasEntries" class="empty-action" @click="goToEmotionRecord">
        <text>开始记录</text>
      </view>
    </view>

    <!-- 情绪选择器弹窗 -->
    <view v-if="showEmotionSelector" class="emotion-selector-modal" @click="hideEmotionPicker">
      <view class="emotion-selector" @click.stop>
        <view class="selector-header">
          <text class="selector-title">选择情绪筛选</text>
          <view class="close-btn" @click="hideEmotionPicker">
            <text>✕</text>
          </view>
        </view>
        <view class="emotion-options">
          <view
            class="emotion-option"
            :class="{ active: selectedEmotionId === null }"
            @click="selectEmotion(null)"
          >
            <text>全部情绪</text>
          </view>
          <view
            v-for="emotion in emotionsStore.emotions"
            :key="emotion.emotion_id"
            class="emotion-option"
            :class="{ active: selectedEmotionId === emotion.emotion_id }"
            @click="selectEmotion(emotion.emotion_id)"
          >
            <view
              class="emotion-icon"
              :style="{ color: emotion.emotion_color }"
            >
              <text>{{ getEmotionIcon(emotion.emotion_icon) }}</text>
            </view>
            <text>{{ emotion.emotion_label }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 条目详情弹窗 -->
    <view v-if="selectedEntry" class="entry-detail-modal" @click="hideEntryDetail">
      <view class="entry-detail" @click.stop>
        <view class="detail-header">
          <text class="detail-title">记录详情</text>
          <view class="close-btn" @click="hideEntryDetail">
            <text>✕</text>
          </view>
        </view>

        <view class="detail-content">
          <view class="detail-emotion">
            <view
              class="emotion-icon"
              :style="{ color: selectedEntry.emotion?.emotion_color }"
            >
              <text>{{ getEmotionIcon(selectedEntry.emotion?.emotion_icon) }}</text>
            </view>
            <text class="emotion-label">{{ selectedEntry.emotion?.emotion_label }}</text>
            <view class="intensity-display">
              <text>强度：</text>
              <view class="intensity-dots">
                <view
                  v-for="i in 5"
                  :key="i"
                  class="dot"
                  :class="{ active: i <= selectedEntry.intensity }"
                />
              </view>
            </view>
          </view>

          <view class="detail-cause">
            <text class="label">原因：</text>
            <text class="value">{{ selectedEntry.cause?.cause_label }}</text>
          </view>

          <view class="detail-time">
            <text class="label">时间：</text>
            <text class="value">{{ formatDetailTime(selectedEntry.date) }}</text>
          </view>

          <view class="detail-notes">
            <text class="label">笔记：</text>
            <text class="value">{{ selectedEntry.notes || '无笔记内容' }}</text>
          </view>
        </view>

        <view class="detail-actions">
          <view class="action-btn edit-btn" @click="editEntry">
            <text>编辑</text>
          </view>
          <view class="action-btn delete-btn" @click="confirmDeleteEntry">
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.diary-page {
  min-height: 100vh;
  background: #f4f1de;
  padding: 0 32rpx;
}

.filter-bar {
  display: flex;
  gap: 24rpx;
  padding: 40rpx 0;
}

.emotion-filter,
.date-filter {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  background: white;
  border-radius: 16rpx;

  .filter-text {
    font-size: 28rpx;
    color: #2a2a2a;
  }

  .filter-icon {
    font-size: 24rpx;
    color: #888888;
  }
}

.entries-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding-bottom: 40rpx;
}

.entry-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.entry-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.emotion-info {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .emotion-icon {
    .icon {
      font-size: 48rpx;
    }
  }

  .intensity-dots {
    display: flex;
    gap: 8rpx;

    .dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background: #eaeaea;

      &.active {
        background: #8ecae6;
      }
    }
  }
}

.entry-time {
  text {
    font-size: 24rpx;
    color: #888888;
  }
}

.entry-content {
  margin-bottom: 24rpx;

  .notes-preview {
    font-size: 28rpx;
    line-height: 1.5;
    color: #2a2a2a;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
  }
}

.entry-footer {
  .cause-tag {
    display: inline-block;
    padding: 8rpx 16rpx;
    background: #f4f1de;
    border-radius: 12rpx;

    text {
      font-size: 24rpx;
      color: #666666;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 40rpx;
  }

  .empty-text {
    display: block;
    font-size: 32rpx;
    color: #2a2a2a;
    margin-bottom: 16rpx;
  }

  .empty-subtitle {
    display: block;
    font-size: 28rpx;
    color: #888888;
    margin-bottom: 40rpx;
  }

  .empty-action {
    display: inline-block;
    padding: 20rpx 40rpx;
    background: #8ecae6;
    color: white;
    border-radius: 16rpx;

    text {
      font-size: 28rpx;
    }
  }
}

// 弹窗样式
.emotion-selector-modal,
.entry-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.emotion-selector,
.entry-detail {
  background: white;
  border-radius: 16rpx;
  margin: 32rpx;
  max-height: 80vh;
  overflow: hidden;
}

.selector-header,
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.selector-title,
.detail-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2a2a2a;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  text {
    font-size: 32rpx;
    color: #888888;
  }
}

.emotion-options {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16rpx 0;
}

.emotion-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 32rpx;

  &.active {
    background: #f4f1de;
  }

  .emotion-icon {
    text {
      font-size: 32rpx;
    }
  }

  text {
    font-size: 28rpx;
    color: #2a2a2a;
  }
}

.detail-content {
  padding: 32rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.detail-emotion {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;

  .emotion-icon {
    text {
      font-size: 48rpx;
    }
  }

  .emotion-label {
    font-size: 32rpx;
    font-weight: 500;
    color: #2a2a2a;
  }

  .intensity-display {
    display: flex;
    align-items: center;
    gap: 8rpx;
    margin-left: auto;

    text {
      font-size: 24rpx;
      color: #888888;
    }

    .intensity-dots {
      display: flex;
      gap: 6rpx;

      .dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background: #eaeaea;

        &.active {
          background: #8ecae6;
        }
      }
    }
  }
}

.detail-cause,
.detail-time,
.detail-notes {
  margin-bottom: 24rpx;

  .label {
    font-size: 28rpx;
    color: #888888;
    margin-right: 16rpx;
  }

  .value {
    font-size: 28rpx;
    color: #2a2a2a;
    line-height: 1.5;
  }
}

.detail-actions {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid #eaeaea;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  border-radius: 12rpx;

  text {
    font-size: 28rpx;
    font-weight: 500;
  }

  &.edit-btn {
    background: #8ecae6;
    color: white;
  }

  &.delete-btn {
    background: #fa5151;
    color: white;
  }
}
</style>
