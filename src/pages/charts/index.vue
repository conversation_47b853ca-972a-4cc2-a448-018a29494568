<script setup lang="ts">
import type { ChartType, TimeRange } from '@/types'
import { computed, ref } from 'vue'
import { useEmotionsStore } from '@/stores'

// Store
const emotionsStore = useEmotionsStore()

// 响应式数据
const currentView = ref<TimeRange>('day')
const currentChartType = ref<ChartType>('emotion')
const currentCauseType = ref<'category' | 'specific'>('category')
const currentDate = ref(new Date())

// 选项配置
const viewOptions = [
  { label: '日', value: 'day' as TimeRange },
  { label: '周', value: 'week' as TimeRange },
  { label: '月', value: 'month' as TimeRange },
]

const chartTypes = [
  { label: '情绪分析', value: 'emotion' as ChartType },
  { label: '原因分析', value: 'cause' as ChartType },
]

const causeTypes = [
  { label: '按类别', value: 'category' },
  { label: '按具体原因', value: 'specific' },
]

// 计算属性
const currentDateText = computed(() => {
  const date = currentDate.value
  if (currentView.value === 'day') {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }
  else if (currentView.value === 'week') {
    return `第${Math.ceil(date.getDate() / 7)}周`
  }
  else {
    return `${date.getFullYear()}年${date.getMonth() + 1}月`
  }
})

const hasData = computed(() => emotionsStore.entries.length > 0)

const timelineData = computed(() => {
  if (currentView.value !== 'day') { return [] }

  return emotionsStore.todayEntries.map(entry => ({
    timestamp: entry.date,
    entry,
  })).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
})

const emotionStats = computed(() => {
  // 简化的统计计算
  const entries = emotionsStore.entries
  const emotionCounts: Record<number, number> = {}

  entries.forEach((entry) => {
    emotionCounts[entry.emotion_id] = (emotionCounts[entry.emotion_id] || 0) + 1
  })

  const total = entries.length

  return Object.entries(emotionCounts).map(([emotionId, count]) => {
    const emotion = emotionsStore.emotions.find(e => e.emotion_id === Number(emotionId))!
    return {
      emotion,
      count,
      percentage: (count / total) * 100,
      avg_intensity: 3, // 简化处理
    }
  }).sort((a, b) => b.count - a.count)
})

const causeStats = computed(() => {
  const entries = emotionsStore.entries
  const causeCounts: Record<number, number> = {}

  entries.forEach((entry) => {
    causeCounts[entry.cause_id] = (causeCounts[entry.cause_id] || 0) + 1
  })

  const total = entries.length

  return Object.entries(causeCounts).map(([causeId, count]) => {
    const cause = emotionsStore.causes.find(c => c.cause_id === Number(causeId))!
    return {
      cause,
      count,
      percentage: (count / total) * 100,
    }
  }).sort((a, b) => b.count - a.count)
})

const currentInsight = computed(() => {
  if (!hasData.value) { return '暂无数据分析' }

  if (currentChartType.value === 'emotion') {
    const topEmotion = emotionStats.value[0]
    if (topEmotion) {
      return `此${currentView.value === 'day' ? '日' : currentView.value === 'week' ? '周' : '月'}内最频繁的情绪：${topEmotion.emotion.emotion_label} (${topEmotion.percentage.toFixed(1)}%)`
    }
  }
  else {
    const topCause = causeStats.value[0]
    if (topCause) {
      const label = currentCauseType.value === 'category' ? topCause.cause.cause_category : topCause.cause.cause_label
      return `常见${currentCauseType.value === 'category' ? '原因类别' : '具体原因'}：${label} (${topCause.percentage.toFixed(1)}%)`
    }
  }

  return '继续记录以获得更多洞察'
})

// 方法
function switchView(view: TimeRange) {
  currentView.value = view
}

function switchChartType(type: ChartType) {
  currentChartType.value = type
}

function switchCauseType(type: 'category' | 'specific') {
  currentCauseType.value = type
}

function showDatePicker() {
  // 简化处理，实际应该显示日期选择器
  uni.showToast({
    title: '日期选择功能开发中',
    icon: 'none',
  })
}

function showEntryDetail(entry: any) {
  uni.showModal({
    title: '记录详情',
    content: `情绪：${entry.emotion?.emotion_label}\n原因：${entry.cause?.cause_label}\n强度：${entry.intensity}/5\n笔记：${entry.notes}`,
    showCancel: false,
  })
}

function formatTimelineTime(timestamp: string) {
  const date = new Date(timestamp)
  const hours = date.getHours()
  const minutes = date.getMinutes()
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}

// 页面生命周期
onShow(() => {
  console.log('图表页面显示')
})
</script>

<template>
  <view class="charts-page">
    <!-- 时间筛选栏 -->
    <view class="filter-bar">
      <view class="date-picker" @click="showDatePicker">
        <text class="date-text">{{ currentDateText }}</text>
        <text class="calendar-icon">📅</text>
      </view>

      <view class="view-switcher">
        <view
          v-for="option in viewOptions"
          :key="option.value"
          class="view-option"
          :class="{ active: currentView === option.value }"
          @click="switchView(option.value)"
        >
          <text>{{ option.label }}</text>
        </view>
      </view>
    </view>

    <!-- 图表类型切换 -->
    <view class="chart-type-switcher">
      <view
        v-for="type in chartTypes"
        :key="type.value"
        class="chart-type"
        :class="{ active: currentChartType === type.value }"
        @click="switchChartType(type.value)"
      >
        <text>{{ type.label }}</text>
      </view>
    </view>

    <!-- 内容区 -->
    <view class="content-area">
      <!-- 情绪分析视图 -->
      <view v-if="currentChartType === 'emotion'" class="emotion-analysis">
        <!-- 情绪时间轴 (仅日视图) -->
        <view v-if="currentView === 'day'" class="timeline-section">
          <view class="section-title">
            今日情绪时间轴
          </view>
          <view class="timeline">
            <view
              v-for="point in timelineData"
              :key="point.entry.entry_id"
              class="timeline-point"
              @click="showEntryDetail(point.entry)"
            >
              <view class="time-label">
                {{ formatTimelineTime(point.timestamp) }}
              </view>
              <view
                class="emotion-dot"
                :style="{ backgroundColor: point.entry.emotion?.emotion_color }"
              >
                <text class="intensity">{{ point.entry.intensity }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 情绪统计列表 -->
        <view class="stats-section">
          <view class="section-title">
            情绪统计
          </view>
          <view class="stats-list">
            <view
              v-for="stat in emotionStats"
              :key="stat.emotion.emotion_id"
              class="stat-item"
            >
              <view class="stat-info">
                <text class="emotion-name">{{ stat.emotion.emotion_label }}</text>
                <text class="stat-count">{{ stat.count }}次 ({{ stat.percentage.toFixed(1) }}%)</text>
              </view>
              <view class="stat-bar">
                <view
                  class="stat-fill"
                  :style="{
                    width: `${stat.percentage}%`,
                    backgroundColor: stat.emotion.emotion_color,
                  }"
                />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 原因分析视图 -->
      <view v-else class="cause-analysis">
        <!-- 原因图表子切换 -->
        <view class="cause-type-switcher">
          <view
            v-for="type in causeTypes"
            :key="type.value"
            class="cause-type"
            :class="{ active: currentCauseType === type.value }"
            @click="switchCauseType(type.value)"
          >
            <text>{{ type.label }}</text>
          </view>
        </view>

        <!-- 原因统计列表 -->
        <view class="stats-section">
          <view class="section-title">
            {{ currentCauseType === 'category' ? '原因类别统计' : '具体原因统计' }}
          </view>
          <view class="stats-list">
            <view
              v-for="stat in causeStats"
              :key="stat.cause.cause_id"
              class="stat-item"
            >
              <view class="stat-info">
                <text class="cause-name">
                  {{ currentCauseType === 'category' ? stat.cause.cause_category : stat.cause.cause_labelstat.cause.cause_category }}
                </text>
                <text class="stat-count">{{ stat.count }}次 ({{ stat.percentage.toFixed(1) }}%)</text>
              </view>
              <view class="stat-bar">
                <view
                  class="stat-fill"
                  :style="{ width: `${stat.percentage}%` }"
                />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 洞察总结 -->
      <view class="insights-section">
        <view class="section-title">
          数据洞察
        </view>
        <view class="insight-card">
          <text class="insight-text">{{ currentInsight }}</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="!hasData" class="empty-state">
        <view class="empty-icon">
          📊
        </view>
        <text class="empty-text">暂无数据</text>
        <text class="empty-subtitle">开始记录情绪来查看分析结果</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.charts-page {
  min-height: 100vh;
  background: #f4f1de;
  padding: 0 32rpx;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 0;
}

.date-picker {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background: white;
  border-radius: 16rpx;

  .date-text {
    font-size: 28rpx;
    color: #2a2a2a;
  }

  .calendar-icon {
    font-size: 24rpx;
  }
}

.view-switcher {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
}

.view-option {
  padding: 16rpx 24rpx;
  border-radius: 12rpx;

  &.active {
    background: #8ecae6;
    color: white;
  }

  text {
    font-size: 28rpx;
  }
}

.chart-type-switcher {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 40rpx;
}

.chart-type {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  border-radius: 12rpx;

  &.active {
    background: #8ecae6;
    color: white;
  }

  text {
    font-size: 32rpx;
    font-weight: 500;
  }
}

.content-area {
  padding-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2a2a2a;
  margin-bottom: 24rpx;
}

.timeline-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.timeline-point {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.time-label {
  font-size: 24rpx;
  color: #888888;
  width: 100rpx;
}

.emotion-dot {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .intensity {
    color: white;
    font-size: 24rpx;
    font-weight: 600;
  }
}

.stats-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.stat-item {
  .stat-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;

    .emotion-name,
    .cause-name {
      font-size: 28rpx;
      color: #2a2a2a;
    }

    .stat-count {
      font-size: 24rpx;
      color: #888888;
    }
  }

  .stat-bar {
    height: 8rpx;
    background: #eaeaea;
    border-radius: 4rpx;
    overflow: hidden;

    .stat-fill {
      height: 100%;
      background: #8ecae6;
      transition: width 0.3s ease;
    }
  }
}

.cause-type-switcher {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
}

.cause-type {
  flex: 1;
  padding: 16rpx;
  text-align: center;
  border-radius: 12rpx;

  &.active {
    background: #8ecae6;
    color: white;
  }

  text {
    font-size: 28rpx;
  }
}

.insights-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.insight-card {
  background: #f4f1de;
  border-radius: 12rpx;
  padding: 24rpx;

  .insight-text {
    font-size: 28rpx;
    line-height: 1.5;
    color: #2a2a2a;
  }
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 40rpx;
  }

  .empty-text {
    display: block;
    font-size: 32rpx;
    color: #2a2a2a;
    margin-bottom: 16rpx;
  }

  .empty-subtitle {
    display: block;
    font-size: 28rpx;
    color: #888888;
  }
}
</style>
