<script setup lang="ts">
import type { Cause, Emotion } from '@/types'
import { computed, ref } from 'vue'
import { useEmotionsStore, useUserStore } from '@/stores'

// Store
const userStore = useUserStore()
const emotionsStore = useEmotionsStore()

// 响应式数据
const selectedCategory = ref<string>('')
const showEmojiSelector = ref(false)
const isSubmitting = ref(false)

// 当前表单数据
const currentForm = computed(() => emotionsStore.currentForm)

// 计算属性
const currentTimeText = computed(() => {
  const now = new Date()
  const hours = now.getHours()
  const minutes = now.getMinutes()
  const period = hours >= 12 ? '下午' : '上午'
  const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours
  return `${period} ${displayHours}:${minutes.toString().padStart(2, '0')} • 今天`
})

const currentCauses = computed(() => {
  if (!selectedCategory.value) { return [] }
  return emotionsStore.getCausesByCategory(selectedCategory.value)
})

const canSubmit = computed(() => {
  return currentForm.value.emotion_id
    && currentForm.value.cause_id
    && currentForm.value.intensity >= 1
    && currentForm.value.intensity <= 5
})

const commonEmojis = [
  '😊',
  '😢',
  '😠',
  '😕',
  '🤔',
  '😴',
  '🤩',
  '💖',
  '👍',
  '👎',
  '💪',
  '🙏',
  '❤️',
  '💔',
  '🌟',
  '⭐',
  '🌈',
  '☀️',
  '🌙',
  '⚡',
  '🔥',
  '💧',
  '🌸',
  '🍀',
]

// 方法
function goBack() {
  if (hasFormData()) {
    uni.showModal({
      title: '确认离开',
      content: '当前记录尚未保存，确定要离开吗？',
      success: (res) => {
        if (res.confirm) {
          resetAndGoBack()
        }
      },
    })
  }
  else {
    resetAndGoBack()
  }
}

function resetAndGoBack() {
  emotionsStore.resetCurrentForm()
  uni.navigateBack()
}

function hasFormData() {
  return currentForm.value.emotion_id
    || currentForm.value.cause_id
    || currentForm.value.notes.trim()
}

function selectEmotion(emotion: Emotion) {
  emotionsStore.updateCurrentForm({ emotion_id: emotion.emotion_id })

  // 添加触觉反馈
  uni.vibrateShort()
}

function selectIntensity(intensity: number) {
  emotionsStore.updateCurrentForm({ intensity })

  // 添加触觉反馈
  uni.vibrateShort()
}

function selectCategory(category: string) {
  selectedCategory.value = category

  // 如果当前选中的原因不属于新分类，清除选择
  if (currentForm.value.cause_id) {
    const currentCause = emotionsStore.causes.find(c => c.cause_id === currentForm.value.cause_id)
    if (currentCause && currentCause.cause_category !== category) {
      emotionsStore.updateCurrentForm({ cause_id: undefined })
    }
  }
}

function selectCause(cause: Cause) {
  emotionsStore.updateCurrentForm({ cause_id: cause.cause_id })

  // 添加触觉反馈
  uni.vibrateShort()
}

function getInputBackgroundColor() {
  if (!currentForm.value.emotion_id) { return '#FFFFFF' }

  const emotion = emotionsStore.emotions.find(e => e.emotion_id === currentForm.value.emotion_id)
  if (!emotion) { return '#FFFFFF' }

  // 将情绪颜色转换为浅色背景
  const color = emotion.emotion_color
  const rgb = hexToRgb(color)
  if (rgb) {
    return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`
  }
  return '#FFFFFF'
}

function hexToRgb(hex: string) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
        r: Number.parseInt(result[1], 16),
        g: Number.parseInt(result[2], 16),
        b: Number.parseInt(result[3], 16),
      }
    : null
}

function showEmojiPicker() {
  showEmojiSelector.value = true
}

function hideEmojiPicker() {
  showEmojiSelector.value = false
}

function insertEmoji(emoji: string) {
  const currentNotes = currentForm.value.notes
  emotionsStore.updateCurrentForm({
    notes: currentNotes + emoji,
  })
  hideEmojiPicker()
}

function startVoiceInput() {
  uni.showToast({
    title: '语音输入功能开发中',
    icon: 'none',
  })
}

async function submitRecord() {
  if (!canSubmit.value || isSubmitting.value) { return }

  // 验证表单
  const validation = emotionsStore.validateForm(currentForm.value)
  if (!validation.isValid) {
    const firstError = Object.values(validation.errors)[0]
    uni.showToast({
      title: firstError,
      icon: 'none',
    })
    return
  }

  isSubmitting.value = true

  try {
    // 添加记录
    const newEntry = emotionsStore.addEntry(currentForm.value, userStore.userId)

    // 显示成功提示
    uni.showToast({
      title: '记录成功',
      icon: 'success',
    })

    // 重置表单
    emotionsStore.resetCurrentForm()

    // 延迟返回，让用户看到成功提示
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  catch (error) {
    console.error('保存记录失败:', error)
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
    })
  }
  finally {
    isSubmitting.value = false
  }
}

function getEmotionIcon(iconName: string) {
  const iconMap: Record<string, string> = {
    'mdi:emoticon-happy-outline': '😊',
    'mdi:emoticon-neutral-outline': '😐',
    'mdi:emoticon-sad-outline': '😢',
    'mdi:emoticon-confused-outline': '😕',
    'mdi:emoticon-angry-outline': '😠',
    'mdi:emoticon-excited-outline': '🤩',
    'mdi:emoticon-dead-outline': '😴',
    'mdi:heart-outline': '💖',
    'mdi:help-circle-outline': '🤔',
  }
  return iconMap[iconName] || '😊'
}

// 页面生命周期
onLoad(() => {
  // 初始化表单
  emotionsStore.resetCurrentForm()

  // 默认选择第一个分类
  if (emotionsStore.causeCategories.length > 0) {
    selectedCategory.value = emotionsStore.causeCategories[0]
  }
})

onUnload(() => {
  // 页面卸载时重置表单
  emotionsStore.resetCurrentForm()
})
</script>

<template>
  <view class="emotion-record-page">
    <!-- 头部 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="timestamp">
        <text>{{ currentTimeText }}</text>
      </view>
    </view>

    <!-- 情绪选择器 -->
    <view class="emotion-selector">
      <view class="section-title">
        你现在的感受是？
      </view>
      <view class="emotion-grid">
        <view
          v-for="emotion in emotionsStore.emotionGrid"
          :key="emotion.emotion_id"
          class="emotion-item"
          :class="{
            selected: currentForm.emotion_id === emotion.emotion_id,
            pulse: currentForm.emotion_id === emotion.emotion_id,
          }"
          @click="selectEmotion(emotion)"
        >
          <view
            class="emotion-icon"
            :style="{
              color: currentForm.emotion_id === emotion.emotion_id ? emotion.emotion_color : '#888888',
            }"
          >
            <text class="icon">{{ getEmotionIcon(emotion.emotion_icon) }}</text>
          </view>
          <text class="emotion-label">{{ emotion.emotion_label }}</text>
        </view>
      </view>
    </view>

    <!-- 强度选择 -->
    <view v-if="currentForm.emotion_id" class="intensity-selector">
      <view class="section-title">
        强度如何？
      </view>
      <view class="intensity-scale">
        <view
          v-for="i in 5"
          :key="i"
          class="intensity-item"
          :class="{ selected: currentForm.intensity === i }"
          @click="selectIntensity(i)"
        >
          <view class="intensity-dot" />
          <text class="intensity-label">{{ i }}</text>
        </view>
      </view>
    </view>

    <!-- 原因分类区 -->
    <view v-if="currentForm.emotion_id" class="cause-selector">
      <view class="section-title">
        是什么原因呢？
      </view>

      <!-- 分类标签栏 -->
      <scroll-view class="category-tabs" scroll-x>
        <view
          v-for="category in emotionsStore.causeCategories"
          :key="category"
          class="category-tab"
          :class="{ active: selectedCategory === category }"
          @click="selectCategory(category)"
        >
          <text>{{ category }}</text>
        </view>
      </scroll-view>

      <!-- 原因选项 -->
      <view class="cause-options">
        <view
          v-for="cause in currentCauses"
          :key="cause.cause_id"
          class="cause-option"
          :class="{ selected: currentForm.cause_id === cause.cause_id }"
          @click="selectCause(cause)"
        >
          <text>{{ cause.cause_label }}</text>
        </view>
      </view>
    </view>

    <!-- 笔记输入区 -->
    <view class="notes-section">
      <view class="section-title">
        想说点什么吗？
      </view>
      <view
        class="notes-input-container"
        :style="{ backgroundColor: getInputBackgroundColor() }"
      >
        <textarea
          v-model="currentForm.notes"
          class="notes-input"
          placeholder="记录下此刻的想法..."
          maxlength="500"
          auto-height
          :adjust-position="false"
        />
        <view class="char-count">
          <text>{{ currentForm.notes.length }}/500</text>
        </view>
      </view>

      <!-- 工具栏 -->
      <view class="input-toolbar">
        <view class="toolbar-btn" @click="showEmojiPicker">
          <text class="toolbar-icon">😊</text>
        </view>
        <view class="toolbar-btn" @click="startVoiceInput">
          <text class="toolbar-icon">🎤</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮区 -->
    <view class="submit-section">
      <view
        class="submit-btn"
        :class="{ disabled: !canSubmit }"
        @click="submitRecord"
      >
        <text>保存记录</text>
      </view>
    </view>

    <!-- 表情选择器弹窗 -->
    <view v-if="showEmojiSelector" class="emoji-selector-modal" @click="hideEmojiPicker">
      <view class="emoji-selector" @click.stop>
        <view class="emoji-header">
          <text class="emoji-title">选择表情</text>
          <view class="close-btn" @click="hideEmojiPicker">
            <text>✕</text>
          </view>
        </view>
        <view class="emoji-grid">
          
          
          <view
            v-for="emoji in commonEmojis"
            :key="emoji"
            class="emoji-item"
            @click="insertEmoji(emoji)"
          >
            <text>{{ emoji }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.emotion-record-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f4f1de 0%, #e9c46a 100%);
  padding: 0 32rpx;
}

.header {
  display: flex;
  align-items: center;
  padding: 40rpx 0;
  position: relative;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .back-icon {
    font-size: 36rpx;
    color: #2a2a2a;
  }
}

.timestamp {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);

  text {
    font-size: 28rpx;
    color: #666666;
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2a2a2a;
  margin-bottom: 32rpx;
  text-align: center;
}

.emotion-selector {
  margin-bottom: 60rpx;
}

.emotion-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
}

.emotion-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: white;
  border-radius: 20rpx;
  transition: all 0.3s ease;

  &.selected {
    background: rgba(142, 202, 230, 0.1);
    border: 2rpx solid #8ecae6;
  }

  &.pulse {
    animation: pulse 0.6s ease-in-out;
  }

  .emotion-icon {
    margin-bottom: 16rpx;

    .icon {
      font-size: 60rpx;
      line-height: 1;
    }
  }

  .emotion-label {
    font-size: 24rpx;
    color: #2a2a2a;
    text-align: center;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.intensity-selector {
  margin-bottom: 60rpx;
}

.intensity-scale {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
}

.intensity-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;

  &.selected {
    .intensity-dot {
      background: #8ecae6;
      transform: scale(1.2);
    }

    .intensity-label {
      color: #8ecae6;
      font-weight: 600;
    }
  }

  .intensity-dot {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    background: #eaeaea;
    transition: all 0.3s ease;
  }

  .intensity-label {
    font-size: 24rpx;
    color: #888888;
    transition: all 0.3s ease;
  }
}

.cause-selector {
  margin-bottom: 60rpx;
}

.category-tabs {
  white-space: nowrap;
  margin-bottom: 32rpx;
}

.category-tab {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 16rpx;
  background: white;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &.active {
    background: #8ecae6;
    color: white;
  }

  text {
    font-size: 28rpx;
    white-space: nowrap;
  }
}

.cause-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.cause-option {
  padding: 20rpx 16rpx;
  background: white;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;

  &.selected {
    background: #8ecae6;
    color: white;
  }

  text {
    font-size: 24rpx;
    line-height: 1.2;
  }
}

.notes-section {
  margin-bottom: 60rpx;
}

.notes-input-container {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  transition: background-color 0.3s ease;
}

.notes-input {
  width: 100%;
  min-height: 200rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #2a2a2a;
  background: transparent;
  border: none;
  outline: none;
}

.char-count {
  text-align: right;
  margin-top: 16rpx;

  text {
    font-size: 24rpx;
    color: #888888;
  }
}

.input-toolbar {
  display: flex;
  gap: 24rpx;
}

.toolbar-btn {
  width: 80rpx;
  height: 80rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

  .toolbar-icon {
    font-size: 32rpx;
  }
}

.submit-section {
  padding-bottom: 40rpx;
}

.submit-btn {
  width: 100%;
  padding: 32rpx;
  background: #8ecae6;
  color: white;
  border-radius: 20rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;

  &.disabled {
    background: #cccccc;
    color: #888888;
  }
}

// 表情选择器弹窗
.emoji-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.emoji-selector {
  background: white;
  border-radius: 16rpx;
  margin: 32rpx;
  max-height: 60vh;
  overflow: hidden;
}

.emoji-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.emoji-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2a2a2a;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  text {
    font-size: 32rpx;
    color: #888888;
  }
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16rpx;
  padding: 32rpx;
  max-height: 40vh;
  overflow-y: auto;
}

.emoji-item {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;

  &:active {
    background: #f4f1de;
  }

  text {
    font-size: 40rpx;
  }
}
</style>
