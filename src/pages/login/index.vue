<script setup lang="ts">
import { ref } from 'vue'
import WeChatLoginButton from '@/components/WeChatLoginButton.vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 响应式状态
const loginError = ref('')
const isLogging = computed(() => userStore.isLogging)

// 登录成功处理
function handleLoginSuccess() {
  loginError.value = ''

  // 显示成功提示
  uni.showToast({
    title: '登录成功',
    icon: 'success',
    duration: 2000,
  })

  // 延迟跳转到主页
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/record/index',
    })
  }, 1500)
}

// 登录失败处理
function handleLoginError(error: string) {
  loginError.value = error

  uni.showToast({
    title: '登录失败',
    icon: 'error',
    duration: 2000,
  })
}

// 页面加载时检查登录状态
onMounted(() => {
  // 如果已经登录，直接跳转到主页
  if (userStore.isLoggedIn) {
    uni.reLaunch({
      url: '/pages/record/index',
    })
  }
})
</script>

<template>
  <view class="login-page flex min-h-screen flex-col bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- 顶部装饰 -->
    <view class="flex flex-1 flex-col items-center justify-center px-8">
      <!-- Logo 区域 -->
      <view class="mb-12 text-center">
        <view class="mx-auto mb-6 flex size-24 items-center justify-center rounded-full bg-blue-500">
          <text class="text-4xl text-white">💭</text>
        </view>
        <text class="mb-2 text-3xl font-bold text-gray-800">今日碎语</text>
        <text class="text-base text-gray-600">记录每一刻的情绪变化</text>
      </view>

      <!-- 登录卡片 -->
      <view class="w-full max-w-sm rounded-2xl bg-white p-8 shadow-lg">
        <view class="mb-8 text-center">
          <text class="mb-2 text-xl font-semibold text-gray-800">欢迎使用</text>
          <text class="text-sm text-gray-600">请使用微信授权登录</text>
        </view>

        <!-- 微信登录按钮 -->
        <WeChatLoginButton
          :loading="isLogging"
          @login-success="handleLoginSuccess"
          @login-error="handleLoginError"
        />

        <!-- 登录状态提示 -->
        <view v-if="loginError" class="mt-4 rounded-lg border border-red-200 bg-red-50 p-3">
          <text class="text-sm text-red-600">{{ loginError }}</text>
        </view>

        <!-- 功能介绍 -->
        <view class="mt-8 space-y-3">
          <view class="flex items-center text-sm text-gray-600">
            <text class="mr-3 size-2 rounded-full bg-blue-500" />
            <text>记录每日情绪变化</text>
          </view>
          <view class="flex items-center text-sm text-gray-600">
            <text class="mr-3 size-2 rounded-full bg-blue-500" />
            <text>分析情绪趋势</text>
          </view>
          <view class="flex items-center text-sm text-gray-600">
            <text class="mr-3 size-2 rounded-full bg-blue-500" />
            <text>生成情绪日记</text>
          </view>
        </view>
      </view>

      <!-- 底部说明 -->
      <view class="mt-8 text-center">
        <text class="text-xs text-gray-500">
          登录即表示同意《用户协议》和《隐私政策》
        </text>
      </view>
    </view>
  </view>
</template>

<style scoped>
.login-page {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
}

/* 动画效果 */
.login-page > view {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮悬停效果 */
.login-page button:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}
</style>
