/**
 * API 服务层
 * 提供统一的 API 接口，支持真实 API 和 Mock 数据
 */

import type {
  ApiResponse,
  AppError,
  Cause,
  CreateCauseRequest,
  CreateEmotionRequest,
  CreateEntryRequest,
  DataExportRequest,
  DataExportResponse,
  Emotion,
  EmotionStatsRequest,
  EmotionStatsResponse,
  Entry,
  PaginationResponse,
  QueryEntriesRequest,
  RefreshTokenRequest,
  UpdateEntryRequest,
  User,
  WxLoginRequest,
  WxLoginResponse,
} from '@/types'

import { ErrorType } from '@/types'

/**
 * API 客户端配置
 */
interface ApiClientConfig {
  /** 基础 URL */
  baseURL: string
  /** 超时时间 (毫秒) */
  timeout: number
  /** 是否启用日志 */
  enableLogging: boolean
  /** 重试次数 */
  retryCount: number
  /** 认证令牌 */
  accessToken?: string
}

/**
 * 请求拦截器
 */
interface RequestInterceptor {
  onRequest?: (config: any) => any
  onRequestError?: (error: any) => any
}

/**
 * 响应拦截器
 */
interface ResponseInterceptor {
  onResponse?: (response: any) => any
  onResponseError?: (error: any) => any
}

/**
 * API 客户端类
 */
class ApiClient {
  private config: ApiClientConfig
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []

  constructor(config: Partial<ApiClientConfig> = {}) {
    this.config = {
      baseURL: 'http://localhost:8080/api',
      timeout: 10000,
      enableLogging: true,
      retryCount: 3,
      ...config,
    }

    this.setupDefaultInterceptors()
  }

  /**
   * 设置默认拦截器
   */
  private setupDefaultInterceptors() {
    // 请求拦截器 - 添加认证头
    this.addRequestInterceptor({
      onRequest: (config) => {
        if (this.config.accessToken) {
          config.header = {
            ...config.header,
            Authorization: `Bearer ${this.config.accessToken}`,
          }
        }

        config.header = {
          ...config.header,
          'Content-Type': 'application/json',
          'X-Client-Version': '1.0.0',
          'X-Platform': 'wechat-miniprogram',
        }

        if (this.config.enableLogging) {
          console.log('API Request:', config)
        }

        return config
      },
      onRequestError: (error) => {
        if (this.config.enableLogging) {
          console.error('API Request Error:', error)
        }
        return Promise.reject(error)
      },
    })

    // 响应拦截器 - 统一错误处理
    this.addResponseInterceptor({
      onResponse: (response) => {
        if (this.config.enableLogging) {
          console.log('API Response:', response)
        }

        // 检查业务状态码
        if (response.data && response.data.code !== 200) {
          const error = this.createApiError(
            ErrorType.SERVER_ERROR,
            response.data.message || '服务器错误',
            response.data,
          )
          return Promise.reject(error)
        }

        return response.data
      },
      onResponseError: (error) => {
        if (this.config.enableLogging) {
          console.error('API Response Error:', error)
        }

        // 网络错误处理
        if (!error.response) {
          return Promise.reject(this.createApiError(
            ErrorType.NETWORK_ERROR,
            '网络连接失败',
            error,
          ))
        }

        // HTTP 状态码错误处理
        const status = error.response.statusCode || error.response.status
        let errorType = ErrorType.SERVER_ERROR
        let message = '服务器错误'

        switch (status) {
          case 401:
            errorType = ErrorType.AUTH_ERROR
            message = '认证失败'
            break
          case 403:
            errorType = ErrorType.AUTH_ERROR
            message = '权限不足'
            break
          case 404:
            errorType = ErrorType.CLIENT_ERROR
            message = '资源不存在'
            break
          case 422:
            errorType = ErrorType.VALIDATION_ERROR
            message = '数据验证失败'
            break
          case 500:
            errorType = ErrorType.SERVER_ERROR
            message = '服务器内部错误'
            break
        }

        return Promise.reject(this.createApiError(errorType, message, error.response.data))
      },
    })
  }

  /**
   * 创建 API 错误
   */
  private createApiError(type: ErrorType, message: string, details?: any): AppError {
    const error = new Error(message) as AppError
    error.type = type
    error.code = type
    error.details = details
    error.retryable = type === ErrorType.NETWORK_ERROR || type === ErrorType.SERVER_ERROR
    error.timestamp = new Date().toISOString()
    return error
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor) {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor) {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 设置访问令牌
   */
  setAccessToken(token: string) {
    this.config.accessToken = token
  }

  /**
   * 清除访问令牌
   */
  clearAccessToken() {
    this.config.accessToken = undefined
  }

  /**
   * 发送请求
   */
  private async request<T>(config: any): Promise<ApiResponse<T>> {
    // 应用请求拦截器
    let requestConfig = config
    for (const interceptor of this.requestInterceptors) {
      if (interceptor.onRequest) {
        try {
          requestConfig = interceptor.onRequest(requestConfig)
        }
        catch (error) {
          if (interceptor.onRequestError) {
            return interceptor.onRequestError(error)
          }
          throw error
        }
      }
    }

    try {
      // 发送真实请求
      const response = await this.sendRealRequest(requestConfig)

      // 应用响应拦截器
      let finalResponse = response
      for (const interceptor of this.responseInterceptors) {
        if (interceptor.onResponse) {
          finalResponse = interceptor.onResponse(finalResponse)
        }
      }

      return finalResponse
    }
    catch (error) {
      // 应用响应错误拦截器
      for (const interceptor of this.responseInterceptors) {
        if (interceptor.onResponseError) {
          return interceptor.onResponseError(error)
        }
      }
      throw error
    }
  }

  /**
   * 发送真实请求
   */
  private async sendRealRequest(config: any): Promise<any> {
    return new Promise((resolve, reject) => {
      uni.request({
        url: `${this.config.baseURL}${config.url}`,
        method: config.method || 'GET',
        data: config.data,
        header: config.header,
        timeout: this.config.timeout,
        success: (response) => {
          resolve(response)
        },
        fail: (error) => {
          reject(error)
        },
      })
    })
  }

  // ==================== 公共 API 方法 ====================

  /**
   * GET 请求
   */
  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'GET',
      data: params,
    })
  }

  /**
   * POST 请求
   */
  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
    })
  }

  /**
   * PUT 请求
   */
  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
    })
  }

  /**
   * DELETE 请求
   */
  async delete<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
    })
  }
}

// 创建全局 API 客户端实例
export const apiClient = new ApiClient({
  enableLogging: true,
})

// ==================== 具体 API 服务方法 ====================

/**
 * 认证相关 API
 */
export const authApi = {
  /**
   * 微信登录
   */
  wxLogin: (data: WxLoginRequest): Promise<ApiResponse<WxLoginResponse>> => {
    return apiClient.post('/auth/wechat-login', data)
  },

  /**
   * 刷新令牌
   */
  refreshToken: (data: RefreshTokenRequest): Promise<ApiResponse<WxLoginResponse>> => {
    return apiClient.post('/auth/refresh-token', data)
  },

  /**
   * 退出登录
   */
  logout: (): Promise<ApiResponse<{ success: boolean }>> => {
    return apiClient.post('/auth/logout')
  },
}

/**
 * 用户相关 API
 */
export const userApi = {
  /**
   * 获取用户信息
   */
  getProfile: (): Promise<ApiResponse<User>> => {
    return apiClient.get('/user/profile')
  },

  /**
   * 更新用户信息
   */
  updateProfile: (data: Partial<User>): Promise<ApiResponse<User>> => {
    return apiClient.put('/user/profile', data)
  },
}

/**
 * 情绪记录相关 API
 */
export const entryApi = {
  /**
   * 创建情绪记录
   */
  create: (data: CreateEntryRequest): Promise<ApiResponse<Entry>> => {
    return apiClient.post('/entries', data)
  },

  /**
   * 查询情绪记录
   */
  query: (params: QueryEntriesRequest): Promise<ApiResponse<PaginationResponse<Entry>>> => {
    return apiClient.get('/entries', params)
  },

  /**
   * 获取单个记录
   */
  getById: (id: number): Promise<ApiResponse<Entry>> => {
    return apiClient.get(`/entries/${id}`)
  },

  /**
   * 更新情绪记录
   */
  update: (id: number, data: UpdateEntryRequest): Promise<ApiResponse<Entry>> => {
    return apiClient.put(`/entries/${id}`, data)
  },

  /**
   * 删除情绪记录
   */
  delete: (id: number): Promise<ApiResponse<{ success: boolean }>> => {
    return apiClient.delete(`/entries/${id}`)
  },
}

/**
 * 情绪定义相关 API
 */
export const emotionApi = {
  /**
   * 获取情绪列表
   */
  getAll: (): Promise<ApiResponse<Emotion[]>> => {
    return apiClient.get('/emotions')
  },

  /**
   * 创建自定义情绪
   */
  create: (data: CreateEmotionRequest): Promise<ApiResponse<Emotion>> => {
    return apiClient.post('/emotions', data)
  },

  /**
   * 更新情绪
   */
  update: (id: number, data: Partial<CreateEmotionRequest>): Promise<ApiResponse<Emotion>> => {
    return apiClient.put(`/emotions/${id}`, data)
  },

  /**
   * 删除情绪
   */
  delete: (id: number): Promise<ApiResponse<{ success: boolean }>> => {
    return apiClient.delete(`/emotions/${id}`)
  },
}

/**
 * 原因定义相关 API
 */
export const causeApi = {
  /**
   * 获取原因列表
   */
  getAll: (): Promise<ApiResponse<Cause[]>> => {
    return apiClient.get('/causes')
  },

  /**
   * 创建自定义原因
   */
  create: (data: CreateCauseRequest): Promise<ApiResponse<Cause>> => {
    return apiClient.post('/causes', data)
  },

  /**
   * 更新原因
   */
  update: (id: number, data: Partial<CreateCauseRequest>): Promise<ApiResponse<Cause>> => {
    return apiClient.put(`/causes/${id}`, data)
  },

  /**
   * 删除原因
   */
  delete: (id: number): Promise<ApiResponse<{ success: boolean }>> => {
    return apiClient.delete(`/causes/${id}`)
  },
}

/**
 * 统计分析相关 API
 */
export const statsApi = {
  /**
   * 获取情绪统计
   */
  getEmotionStats: (params: EmotionStatsRequest): Promise<ApiResponse<EmotionStatsResponse>> => {
    return apiClient.get('/stats/emotions', params)
  },

  /**
   * 获取原因统计
   */
  getCauseStats: (params: EmotionStatsRequest): Promise<ApiResponse<EmotionStatsResponse>> => {
    return apiClient.get('/stats/causes', params)
  },

  /**
   * 获取强度统计
   */
  getIntensityStats: (params: EmotionStatsRequest): Promise<ApiResponse<EmotionStatsResponse>> => {
    return apiClient.get('/stats/intensity', params)
  },
}

/**
 * 数据导出相关 API
 */
export const exportApi = {
  /**
   * 导出数据
   */
  exportData: (params: DataExportRequest): Promise<ApiResponse<DataExportResponse>> => {
    return apiClient.post('/export', params)
  },

  /**
   * 获取导出状态
   */
  getExportStatus: (exportId: string): Promise<ApiResponse<{ status: string, progress: number }>> => {
    return apiClient.get(`/export/${exportId}/status`)
  },
}
export const api = {
  auth: authApi,
  user: userApi,
  entry: entryApi,
  emotion: emotionApi,
  cause: causeApi,
  stats: statsApi,
  export: exportApi,
}

export default apiClient
