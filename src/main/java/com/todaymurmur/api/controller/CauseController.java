package com.todaymurmur.api.controller;

import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.todaymurmur.api.dto.request.CauseRequest;
import com.todaymurmur.api.dto.response.ApiResponse;
import com.todaymurmur.api.model.Cause;
import com.todaymurmur.api.model.User;
import com.todaymurmur.api.repository.CauseRepository;
import com.todaymurmur.api.repository.UserRepository;
import com.todaymurmur.api.util.CauseDTO;
import com.todaymurmur.api.util.EntityDTOUtil;

@RestController
@RequestMapping("/causes")
public class CauseController extends BaseController {

    @Autowired
    private CauseRepository causeRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EntityDTOUtil entityDTOUtil;

    @GetMapping
    public ApiResponse<List<CauseDTO>> getAllCauses() {
        try {
            User currentUser = getCurrentUser(userRepository);
            List<Cause> causes = causeRepository.findAllAvailableForUser(currentUser);
            List<CauseDTO> causeDTOs = causes.stream().map(entityDTOUtil::toCauseDTO).collect(Collectors.toList());

            return success(causeDTOs);
        } catch (Exception e) {
            logger.error("Error retrieving all causes", e);
            return error();
        }
    }

    @GetMapping("/system")
    public ApiResponse<List<CauseDTO>> getSystemCauses() {
        try {
            List<Cause> causes = causeRepository.findByIsCustomFalseAndIsDeletedFalse();
            List<CauseDTO> causeDTOs = causes.stream().map(entityDTOUtil::toCauseDTO).collect(Collectors.toList());

            return success(causeDTOs);
        } catch (Exception e) {
            logger.error("Error retrieving system causes", e);
            return error();
        }
    }

    @GetMapping("/custom")
    public ApiResponse<List<CauseDTO>> getCustomCauses() {
        try {
            User currentUser = getCurrentUser(userRepository);
            List<Cause> causes = causeRepository.findByUserAndIsCustomTrueAndIsDeletedFalse(currentUser);
            List<CauseDTO> causeDTOs = causes.stream().map(entityDTOUtil::toCauseDTO).collect(Collectors.toList());

            return success(causeDTOs, "Custom causes retrieved successfully");
        } catch (Exception e) {
            logger.error("Error retrieving custom causes", e);
            return error();
        }
    }

    @PostMapping("/custom")
    public ApiResponse<CauseDTO> createCustomCause(@Valid @RequestBody CauseRequest request) {
        try {
            User currentUser = getCurrentUser(userRepository);

            Cause cause = new Cause();
            cause.setCauseLabel(request.getCauseLabel());
            cause.setCauseCategory(request.getCategoryName());
            cause.setIsCustom(true);
            cause.setUser(currentUser);

            Cause savedCause = causeRepository.save(cause);
            CauseDTO causeDTO = entityDTOUtil.toCauseDTO(savedCause);

            return success(causeDTO, "Custom cause created successfully");
        } catch (Exception e) {
            logger.error("Error creating custom cause", e);
            return error();
        }
    }

    @PutMapping("/custom/{id}")
    public ApiResponse<CauseDTO> updateCustomCause(@PathVariable Long id, @Valid @RequestBody CauseRequest request) {
        try {
            User currentUser = getCurrentUser(userRepository);

            Cause cause = causeRepository.findByCauseIdAndUser(id, currentUser)
                .orElseThrow(() -> new RuntimeException("Cause label not found or not owned by user"));

            cause.setCauseLabel(request.getCauseLabel());
            cause.setCauseCategory(request.getCategoryName());

            Cause updatedCause = causeRepository.save(cause);
            CauseDTO causeDTO = entityDTOUtil.toCauseDTO(updatedCause);

            return success(causeDTO, "Custom cause updated successfully");
        } catch (RuntimeException e) {
            logger.error("Error updating custom cause", e);
            return error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            logger.error("Error updating custom cause", e);
            return error();
        }
    }

    @DeleteMapping("/custom/{id}")
    public ApiResponse<Void> deleteCustomCause(@PathVariable Long id) {
        try {
            causeRepository.deleteById(id);
            return success();
        } catch (Exception e) {
            logger.error("Error deleting custom cause", e);
            return error();
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(CauseController.class);
}
