package com.todaymurmur.api.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.todaymurmur.api.dto.UserInfoDTO;
import com.todaymurmur.api.dto.response.ApiResponse;
import com.todaymurmur.api.model.User;
import com.todaymurmur.api.repository.UserRepository;

@RestController
@RequestMapping("/user")
public class UserController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    @Autowired
    private UserRepository userRepository;

    @GetMapping("/profile")
    public ApiResponse<UserInfoDTO> getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();

            logger.debug("Getting user info for: {}", username);

            // Try to find by openId first (for WeChat users), then by username
            User user = userRepository.findByOpenId(username).orElseGet(() -> userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found with identifier: " + username)));

            // Create a response with detailed user information
            UserInfoDTO userInfo = new UserInfoDTO();
            userInfo.setId(user.getUserId());
            userInfo.setUsername(user.getUsername());
            userInfo.setNickname(user.getNickname());
            userInfo.setAvatarUrl(user.getAvatarUrl());
            userInfo.setCreatedAt(user.getCreatedAt());
            userInfo.setLastLoginTime(user.getLastLoginTime());

            logger.debug("User info retrieved successfully for: {}", username);
            return success(userInfo);
        } catch (RuntimeException e) {
            logger.error("User not found: {}", e.getMessage());
            return error(HttpStatus.NOT_FOUND.value(), "User not found: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Failed to get user info: {}", e.getMessage());
            return error();
        }
    }

}
