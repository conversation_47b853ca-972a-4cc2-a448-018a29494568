package com.todaymurmur.api.controller;

import java.time.LocalDateTime;
import java.util.ArrayList;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.todaymurmur.api.dto.JwtDTO;
import com.todaymurmur.api.dto.request.WeChatLoginRequest;
import com.todaymurmur.api.dto.response.ApiResponse;
import com.todaymurmur.api.model.User;
import com.todaymurmur.api.repository.UserRepository;
import com.todaymurmur.api.security.JwtTokenProvider;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/auth")
public class AuthController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    UserRepository userRepository;

    @Autowired
    JwtTokenProvider tokenProvider;

    @Autowired
    private WxMaService wxMaService;

    @PostMapping("/wechat-login")
    public ApiResponse<JwtDTO> login(@Valid @RequestBody WeChatLoginRequest loginRequest) {
        logger.info("WeChat login request received with code: {}", loginRequest.getCode());

        try {
            // Get session info from WeChat
            logger.debug("Getting session info from WeChat API");
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(loginRequest.getCode());
            logger.debug("Received session info from WeChat. OpenID: {}", sessionInfo.getOpenid());

            if (sessionInfo.getOpenid() == null || sessionInfo.getOpenid().isEmpty()) {
                logger.error("WeChat returned empty openid for code: {}", loginRequest.getCode());
                return error(HttpStatus.BAD_REQUEST.value(), "WeChat login failed: Invalid response from WeChat API");
            }

            // Check if user exists
            User user = userRepository.findByOpenId(sessionInfo.getOpenid()).orElseGet(() -> {
                // Create new user if not exists
                logger.info("Creating new user with openId: {}", sessionInfo.getOpenid());
                User newUser = new User();
                newUser.setOpenId(sessionInfo.getOpenid());
                newUser.setUnionId(sessionInfo.getUnionid());
                newUser.setSessionKey(sessionInfo.getSessionKey());
                newUser.setNickname(loginRequest.getNickname());
                newUser.setUsername("wechat::" + sessionInfo.getOpenid());
                newUser.setAvatarUrl(loginRequest.getAvatarUrl());
                newUser.setCreatedAt(LocalDateTime.now());
                newUser.setLastLoginTime(LocalDateTime.now());
                return userRepository.save(newUser);
            });

            // Update user info if needed
            if (loginRequest.getNickname() != null && !loginRequest.getNickname().equals(user.getNickname())) {
                user.setNickname(loginRequest.getNickname());
            }

            if (loginRequest.getAvatarUrl() != null && !loginRequest.getAvatarUrl().equals(user.getAvatarUrl())) {
                user.setAvatarUrl(loginRequest.getAvatarUrl());
            }

            // Always update session key and last login time
            logger.debug("Updating session key for user: {}", user.getOpenId());
            user.setSessionKey(sessionInfo.getSessionKey());
            user.setLastLoginTime(LocalDateTime.now());

            user = userRepository.save(user);
            logger.debug("User information updated successfully");

            try {
                // Authenticate user
                logger.debug("Authenticating user with openId: {}", user.getOpenId());

                // Create UserDetails object for authentication
                UserDetails userDetails = new org.springframework.security.core.userdetails.User(user.getUsername(),
                    user.getSessionKey(), new ArrayList<>());

                // Create authentication token with UserDetails
                Authentication authentication =
                    new UsernamePasswordAuthenticationToken(userDetails, user.getSessionKey(), new ArrayList<>());

                // Set authentication in security context
                SecurityContextHolder.getContext().setAuthentication(authentication);

                // Generate JWT token
                String jwt = tokenProvider.generateToken(authentication);
                logger.info("WeChat login successful for user: {}", user.getUsername());

                // Create response
                JwtDTO jwtDTO = new JwtDTO(jwt, user.getUserId(),
                    user.getNickname() != null ? user.getNickname() : "用户" + user.getUsername());

                return success(jwtDTO, "WeChat login successful");
            } catch (Exception authEx) {
                logger.error("Authentication failed for WeChat user: {}", user.getUsername(), authEx);

                // As a fallback, we can generate a token directly without authentication
                // This is a workaround for session key mismatches
                UserDetails userDetails =
                    new org.springframework.security.core.userdetails.User(user.getUsername(), "", new ArrayList<>());

                Authentication authentication =
                    new UsernamePasswordAuthenticationToken(userDetails, "", new ArrayList<>());

                SecurityContextHolder.getContext().setAuthentication(authentication);
                String jwt = tokenProvider.generateToken(authentication);
                logger.info("Generated fallback token for WeChat user: {}", user.getUsername());

                JwtDTO jwtDTO = new JwtDTO(jwt, user.getUserId(),
                    user.getNickname() != null ? user.getNickname() : "用户" + user.getUserId());
                jwtDTO.setUser(user);

                return success(jwtDTO, "WeChat login successful (fallback)");
            }
        } catch (Exception e) {
            logger.error("WeChat login failed", e);
            return error();
        }
    }

}
