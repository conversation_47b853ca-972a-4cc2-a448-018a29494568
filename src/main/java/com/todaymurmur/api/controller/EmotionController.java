package com.todaymurmur.api.controller;

import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.todaymurmur.api.dto.request.EmotionRequest;
import com.todaymurmur.api.dto.response.ApiResponse;
import com.todaymurmur.api.model.Emotion;
import com.todaymurmur.api.model.User;
import com.todaymurmur.api.repository.EmotionRepository;
import com.todaymurmur.api.repository.UserRepository;
import com.todaymurmur.api.util.EmotionDTO;
import com.todaymurmur.api.util.EntityDTOUtil;

@RestController
@RequestMapping("/emotions")
public class EmotionController extends BaseController {

    @Autowired
    private EmotionRepository emotionRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EntityDTOUtil entityDTOUtil;

    @GetMapping
    public ApiResponse<List<EmotionDTO>> getAllEmotions() {
        User currentUser = getCurrentUser(userRepository);
        List<Emotion> emotions = emotionRepository.findAllAvailableForUser(currentUser);
        List<EmotionDTO> emotionDTOs = emotions.stream().map(entityDTOUtil::toEmotionDTO).collect(Collectors.toList());

        return success(emotionDTOs, "All emotions retrieved successfully");
    }

    @GetMapping("/system")
    public ApiResponse<List<EmotionDTO>> getSystemEmotions() {
        List<Emotion> emotions = emotionRepository.findByIsCustomFalseAndIsDeletedFalse();
        List<EmotionDTO> emotionDTOs = emotions.stream().map(entityDTOUtil::toEmotionDTO).collect(Collectors.toList());

        return success(emotionDTOs, "System emotions retrieved successfully");
    }

    @GetMapping("/custom")
    public ApiResponse<List<EmotionDTO>> getCustomEmotions() {
        User currentUser = getCurrentUser(userRepository);
        List<Emotion> emotions = emotionRepository.findByUserAndIsCustomTrueAndIsDeletedFalse(currentUser);
        List<EmotionDTO> emotionDTOs = emotions.stream().map(entityDTOUtil::toEmotionDTO).collect(Collectors.toList());

        return success(emotionDTOs, "Custom emotions retrieved successfully");
    }

    @PostMapping("/custom")
    public ApiResponse<EmotionDTO> createCustomEmotion(@Valid @RequestBody EmotionRequest request) {
        try {
            User currentUser = getCurrentUser(userRepository);

            Emotion emotion = new Emotion();
            emotion.setEmotionLabel(request.getEmotionLabel());
            emotion.setEmotionIcon(request.getEmotionIcon());
            emotion.setEmotionColor(request.getEmotionColor());
            emotion.setIsCustom(true);
            emotion.setUser(currentUser);

            Emotion savedEmotion = emotionRepository.save(emotion);
            EmotionDTO emotionDTO = entityDTOUtil.toEmotionDTO(savedEmotion);

            return success(emotionDTO, "Custom emotion created successfully");
        } catch (Exception e) {
            logger.error("Error creating custom emotion", e);
            return error();
        }
    }

    @PutMapping("/custom/{id}")
    public ApiResponse<EmotionDTO> updateCustomEmotion(@PathVariable Long id,
        @Valid @RequestBody EmotionRequest request) {
        try {
            User currentUser = getCurrentUser(userRepository);

            Emotion emotion = emotionRepository.findByEmotionIdAndUser(id, currentUser)
                .orElseThrow(() -> new RuntimeException("Emotion label not found or not owned by user"));

            emotion.setEmotionLabel(request.getEmotionLabel());
            emotion.setEmotionIcon(request.getEmotionIcon());
            emotion.setEmotionColor(request.getEmotionColor());

            Emotion updatedEmotion = emotionRepository.save(emotion);
            EmotionDTO emotionDTO = entityDTOUtil.toEmotionDTO(updatedEmotion);

            return success(emotionDTO, "Custom emotion updated successfully");
        } catch (RuntimeException e) {
            logger.error("Error updating custom emotion", e);
            return error(HttpStatus.NOT_FOUND.value(), e.getMessage());

        } catch (Exception e) {
            logger.error("Error updating custom emotion", e);
            return error();
        }
    }

    @DeleteMapping("/custom/{id}")
    public ApiResponse<String> deleteCustomEmotion(@PathVariable Long id) {
        try {
            User currentUser = getCurrentUser(userRepository);

            Emotion emotion = emotionRepository.findByEmotionIdAndUser(id, currentUser)
                .orElseThrow(() -> new RuntimeException("Emotion label not found or not owned by user"));

            emotionRepository.delete(emotion);

            return success("Emotion label deleted successfully");
        } catch (RuntimeException e) {
            logger.error("Error deleting custom emotion", e);
            return error(HttpStatus.NOT_FOUND.value(), e.getMessage());

        } catch (Exception e) {
            logger.error("Error deleting custom emotion", e);
            return error();
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(EmotionController.class);
}
