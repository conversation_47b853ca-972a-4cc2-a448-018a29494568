package com.todaymurmur.api.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.todaymurmur.api.dto.EntryDTO;
import com.todaymurmur.api.dto.request.EntryRequest;
import com.todaymurmur.api.dto.response.ApiResponse;
import com.todaymurmur.api.model.Cause;
import com.todaymurmur.api.model.Emotion;
import com.todaymurmur.api.model.Entry;
import com.todaymurmur.api.model.User;
import com.todaymurmur.api.repository.CauseRepository;
import com.todaymurmur.api.repository.EmotionRepository;
import com.todaymurmur.api.repository.EntryRepository;
import com.todaymurmur.api.repository.UserRepository;
import com.todaymurmur.api.security.EncryptionService;

@RestController
@RequestMapping("/entries")
public class EntryController extends BaseController {

    @Autowired
    private EntryRepository entryRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmotionRepository emotionRepository;

    @Autowired
    private CauseRepository causeRepository;

    @Autowired
    private EncryptionService encryptionService;

    @GetMapping
    public ApiResponse<Page<EntryDTO>> getEntries(Pageable pageable) {
        try {
            User currentUser = getCurrentUser(userRepository);
            Page<Entry> entries = entryRepository.findByUserOrderByDateDesc(currentUser, pageable);
            Page<EntryDTO> entryResponses = entries.map(this::convertToEntryResponse);

            return success(entryResponses, "Entries retrieved successfully");
        } catch (Exception e) {
            logger.error("Error retrieving entries", e);
            return error();
        }
    }

    @GetMapping("/{id}")
    public ApiResponse<EntryDTO> getEntry(@PathVariable Long id) {
        try {
            User currentUser = getCurrentUser(userRepository);
            Entry entry = entryRepository.findByEntryIdAndUser(id, currentUser)
                .orElseThrow(() -> new RuntimeException("Entry not found"));

            EntryDTO entryDto = convertToEntryResponse(entry);
            return success(entryDto, "Entry retrieved successfully");
        } catch (RuntimeException e) {
            logger.error("Entry not found", e);
            return error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            logger.error("Error retrieving entry", e);
            return error();
        }
    }

    @PostMapping
    public ApiResponse<EntryDTO> createEntry(@Valid @RequestBody EntryRequest entryRequest) {
        try {
            User currentUser = getCurrentUser(userRepository);

            // Create new entry
            Entry entry = new Entry();
            entry.setUser(currentUser);
            entry.setDate(entryRequest.getDate());

            // Encrypt content
            String encryptedContent = encryptionService.encrypt(entryRequest.getNotes());
            entry.setNotes(encryptedContent);

            // Process emotion
            if (entryRequest.getEmotionId() != null) {
                Emotion emotion = findEmotion(entryRequest.getEmotionId(), currentUser);
                entry.setEmotion(emotion);
            }
            entry.setIntensity(entryRequest.getIntensity());

            // Process cause
            if (entryRequest.getCauseId() != null) {
                Cause cause = findCause(entryRequest.getCauseId(), currentUser);
                entry.setCause(cause);
            }

            // Save entry first to get ID
            Entry savedEntry = entryRepository.save(entry);
            EntryDTO entryDto = convertToEntryResponse(savedEntry);

            return success(entryDto, "Entry created successfully");
        } catch (RuntimeException e) {
            logger.error("Error creating entry", e);
            return error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            logger.error("Error creating entry", e);
            return error();
        }
    }

    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteEntry(@PathVariable Long id) {
        try {
            User currentUser = getCurrentUser(userRepository);
            Entry entry = entryRepository.findByEntryIdAndUser(id, currentUser)
                .orElseThrow(() -> new RuntimeException("Entry not found"));

            entryRepository.delete(entry);

            return success();
        } catch (RuntimeException e) {
            logger.error("Entry not found", e);
            return error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            logger.error("Error deleting entry", e);
            return error();
        }
    }

    @GetMapping("/date-range")
    public ApiResponse<List<EntryDTO>> getEntriesByDateRange(
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            User currentUser = getCurrentUser(userRepository);
            List<Entry> entries =
                entryRepository.findByUserAndDateBetweenOrderByDateDesc(currentUser, startDate, endDate);
            List<EntryDTO> entryRespons =
                entries.stream().map(this::convertToEntryResponse).collect(Collectors.toList());

            return success(entryRespons, "Entries retrieved successfully");
        } catch (Exception e) {
            logger.error("Error retrieving entries by date range", e);
            return error();
        }
    }

    @GetMapping("/{id}/related")
    public ApiResponse<List<EntryDTO>> getRelatedEntries(@PathVariable Long id,
        @RequestParam(defaultValue = "3") int limit) {
        try {
            User currentUser = getCurrentUser(userRepository);
            Entry entry = entryRepository.findByEntryIdAndUser(id, currentUser)
                .orElseThrow(() -> new RuntimeException("Entry not found"));

            // Find entries with the same emotion or cause
            Page<Entry> userEntries = entryRepository.findByUserOrderByDateDesc(currentUser, Pageable.ofSize(10));

            List<Entry> relatedEntries = userEntries.stream().filter(e -> !e.getEntryId().equals(id)) // Exclude the
                                                                                                      // current entry
                .filter(e -> (e.getEmotion() != null && entry.getEmotion() != null
                    && e.getEmotion().getEmotionId().equals(entry.getEmotion().getEmotionId()))
                    || (e.getCause() != null && entry.getCause() != null
                        && e.getCause().getCauseId().equals(entry.getCause().getCauseId())))
                .limit(limit).collect(Collectors.toList());

            List<EntryDTO> responseList =
                relatedEntries.stream().map(this::convertToEntryResponse).collect(Collectors.toList());

            return success(responseList, "Related entries retrieved successfully");
        } catch (RuntimeException e) {
            logger.error("Entry not found", e);
            return error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            logger.error("Error retrieving related entries", e);
            return error();
        }
    }

    private EntryDTO convertToEntryResponse(Entry entry) {
        EntryDTO response = new EntryDTO();
        response.setId(entry.getEntryId());

        // Decrypt content
        String decryptedContent = encryptionService.decrypt(entry.getNotes());
        response.setNotes(decryptedContent);

        response.setDate(entry.getDate());
        response.setCreatedAt(entry.getCreatedAt());
        response.setUpdatedAt(entry.getUpdatedAt());

        // Convert emotion
        if (entry.getEmotion() != null) {
            EntryDTO.EmotionDto emotionDto = new EntryDTO.EmotionDto();
            emotionDto.setId(entry.getEmotion().getEmotionId());
            emotionDto.setLabel(entry.getEmotion().getEmotionLabel());
            emotionDto.setIcon(entry.getEmotion().getEmotionIcon());
            emotionDto.setColor(entry.getEmotion().getEmotionColor());
            emotionDto.setIntensity(entry.getIntensity());
            response.setEmotion(emotionDto);
        }

        // Convert cause
        if (entry.getCause() != null) {
            EntryDTO.CauseDto causeDto = new EntryDTO.CauseDto();
            causeDto.setId(entry.getCause().getCauseId());
            causeDto.setLabel(entry.getCause().getCauseLabel());
            response.setCause(causeDto);
        }

        return response;
    }

    private static final Logger logger = LoggerFactory.getLogger(EntryController.class);

    private Emotion findEmotion(Long emotionId, User currentUser) {
        // Try to find emotion by ID
        return emotionRepository.findById(emotionId)
            .orElseThrow(() -> new RuntimeException("Emotion with ID " + emotionId + " not found"));
    }

    private Cause findCause(Long causeId, User currentUser) {
        // Try to find cause by ID
        return causeRepository.findById(causeId)
            .orElseThrow(() -> new RuntimeException("Cause with ID " + causeId + " not found"));
    }
}
