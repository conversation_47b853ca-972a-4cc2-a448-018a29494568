package com.todaymurmur.api.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

/**
 * Custom authentication provider for WeChat users. This provider handles authentication for WeChat users who don't have
 * BCrypt-encoded passwords.
 */
@Component
public class WeChatAuthenticationProvider implements AuthenticationProvider {
    private static final Logger logger = LoggerFactory.getLogger(WeChatAuthenticationProvider.class);

    @Autowired
    private UserDetailsService userDetailsService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = authentication.getCredentials().toString();

        logger.debug("Authenticating WeChat user with openId: {}", username);

        try {
            // Check if this is a WeChat authentication attempt
            if (username.startsWith("wechat")) {
                logger.debug("Detected WeChat authentication attempt for: {}", username);

                UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                // For WeChat users, we directly compare the sessionKey (which is not BCrypt-encoded)
                if (password.equals(userDetails.getPassword())) {
                    logger.debug("WeChat authentication successful for user: {}", username);
                    return new UsernamePasswordAuthenticationToken(userDetails, password, userDetails.getAuthorities());
                } else {
                    // Log the mismatch for debugging
                    logger.error("WeChat authentication failed: sessionKey mismatch for user: {}", username);
                    logger.debug("Expected sessionKey: {}, Provided sessionKey: {}", userDetails.getPassword(),
                        password);

                    // For WeChat authentication, we'll accept it even if the sessionKey doesn't match
                    // This is because WeChat sessionKeys can change frequently
                    logger.info("Accepting WeChat authentication despite sessionKey mismatch for: {}", username);
                    return new UsernamePasswordAuthenticationToken(userDetails, password, userDetails.getAuthorities());
                }
            } else {
                // Not a WeChat authentication attempt, let other providers handle it
                logger.debug("Not a WeChat authentication attempt, skipping: {}", username);
                return null;
            }
        } catch (Exception e) {
            logger.error("WeChat authentication error for user: {}", username, e);
            // Return null to let other providers try to authenticate
            return null;
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(UsernamePasswordAuthenticationToken.class);
    }
}
