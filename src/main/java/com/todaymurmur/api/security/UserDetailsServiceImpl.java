package com.todaymurmur.api.security;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.todaymurmur.api.model.User;
import com.todaymurmur.api.repository.UserRepository;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // Try to find by openId first (for WeChat users)
        User user = userRepository.findByUsername(username).orElseGet(() -> userRepository.findByOpenId(username)
            .orElseThrow(() -> new UsernameNotFoundException("User not found with identifier: " + username)));

        return new org.springframework.security.core.userdetails.User(user.getUsername(), // Use openId as the principal
            user.getSessionKey() != null ? user.getSessionKey() : "", // Use sessionKey as credentials
            new ArrayList<>());
    }
}
