package com.todaymurmur.api.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.security.authentication.event.AbstractAuthenticationFailureEvent;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.authentication.event.InteractiveAuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.todaymurmur.api.util.SecurityUtils;

/**
 * Listener for authentication events.
 * Logs authentication successes and failures.
 */
@Component
public class AuthenticationEventListener {
    private static final Logger logger = LoggerFactory.getLogger(AuthenticationEventListener.class);

    /**
     * Handle authentication success events.
     */
    @EventListener
    public void onSuccess(AuthenticationSuccessEvent event) {
        Authentication authentication = event.getAuthentication();
        logger.info("Authentication success: {}, Principal: {}", 
                authentication.getClass().getSimpleName(),
                authentication.getName());
    }

    /**
     * Handle interactive authentication success events.
     */
    @EventListener
    public void onSuccess(InteractiveAuthenticationSuccessEvent event) {
        Authentication authentication = event.getAuthentication();
        logger.info("Interactive authentication success: {}, Principal: {}", 
                authentication.getClass().getSimpleName(),
                authentication.getName());
    }

    /**
     * Handle authentication failure events.
     */
    @EventListener
    public void onFailure(AbstractAuthenticationFailureEvent event) {
        Authentication authentication = event.getAuthentication();
        logger.error("Authentication failure: {}, Principal: {}, Exception: {}", 
                authentication.getClass().getSimpleName(),
                authentication.getName(),
                event.getException().getMessage());
        
        // Clear cache for this user to ensure fresh data on next attempt
        SecurityUtils.removeFromCache(authentication.getName());
    }
}
