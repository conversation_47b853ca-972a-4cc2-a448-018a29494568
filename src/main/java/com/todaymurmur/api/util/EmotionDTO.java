package com.todaymurmur.api.util;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EmotionDTO {
    private Long emotionId;
    private String emotionLabel;
    private String emotionIcon;
    private String emotionColor;
    private Boolean isCustom;
    private Boolean isDeleted;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long userId;
}
