package com.todaymurmur.api.repository;

import com.todaymurmur.api.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
    Optional<User> findByOpenId(String openId);
    Boolean existsByOpenId(String openId);
}
