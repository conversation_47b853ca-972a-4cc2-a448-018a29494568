package com.todaymurmur.api.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.todaymurmur.api.model.Cause;
import com.todaymurmur.api.model.User;

@Repository
public interface CauseRepository extends JpaRepository<Cause, Long> {
    List<Cause> findByIsCustomFalseAndIsDeletedFalse();

    List<Cause> findByUserAndIsCustomTrueAndIsDeletedFalse(User user);

    @Query("SELECT t FROM Cause t WHERE t.isCustom = false OR (t.isCustom = true AND t.user = ?1) AND t.isDeleted = false")
    List<Cause> findAllAvailableForUser(User user);

    Optional<Cause> findByCauseIdAndUser(Long causeId, User user);
}
