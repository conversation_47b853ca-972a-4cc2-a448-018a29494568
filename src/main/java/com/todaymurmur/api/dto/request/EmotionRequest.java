package com.todaymurmur.api.dto.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import lombok.Data;

@Data
public class EmotionRequest {
    @NotBlank
    private String emotionLabel;

    @NotBlank
    private String emotionIcon;

    @NotBlank
    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", message = "Color must be a valid hex color code")
    private String emotionColor;
}
