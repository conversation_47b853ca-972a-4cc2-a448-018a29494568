package com.todaymurmur.api.dto.request;

import java.time.LocalDateTime;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class EntryRequest {
    @NotBlank
    private String notes;

    @NotNull
    private LocalDateTime date;

    @NotNull
    private Long emotionId;

    @NotNull
    private Integer intensity;

    @NotNull
    private Long causeId;

}
