<script setup lang="ts">
import { computed, ref } from 'vue'
import { useUserStore } from '@/stores/user'
import WeChatLoginButton from './WeChatLoginButton.vue'

// Store
const userStore = useUserStore()

// 响应式状态
const isLoggingOut = ref(false)

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const userName = computed(() => userStore.userName)
const userAvatar = computed(() => userStore.userAvatar)
const userId = computed(() => userStore.userId)

// 模拟统计数据（实际应该从 store 或 API 获取）
const totalEntries = ref(0)
const consecutiveDays = ref(0)
const avgMood = ref('😊')

// 最后登录时间文本
const lastLoginText = computed(() => {
  if (!userStore.userInfo?.last_login_time) { return '首次登录' }

  const lastLogin = new Date(userStore.userInfo.last_login_time)
  const now = new Date()
  const diffMs = now.getTime() - lastLogin.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) { return '今天登录' }
  if (diffDays === 1) { return '昨天登录' }
  return `${diffDays}天前登录`
})

// 头像加载错误处理
function handleAvatarError() {
  console.warn('用户头像加载失败')
}

// 打开设置
function openSettings() {
  uni.navigateTo({
    url: '/pages/profile/settings',
  })
}

// 跳转到记录页面
function goToRecord() {
  uni.switchTab({
    url: '/pages/record/index',
  })
}

// 跳转到图表页面
function goToCharts() {
  uni.switchTab({
    url: '/pages/charts/index',
  })
}

// 退出登录
async function handleLogout() {
  try {
    isLoggingOut.value = true

    // 显示确认对话框
    const result = await new Promise<boolean>((resolve) => {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: res => resolve(res.confirm),
        fail: () => resolve(false),
      })
    })

    if (!result) { return }

    await userStore.logout()

    uni.showToast({
      title: '已退出登录',
      icon: 'success',
    })

    // 跳转到登录页面
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/index',
      })
    }, 1500)
  }
  catch (error) {
    console.error('退出登录失败:', error)
    uni.showToast({
      title: '退出失败',
      icon: 'error',
    })
  }
  finally {
    isLoggingOut.value = false
  }
}

// 登录成功处理
function handleLoginSuccess() {
  uni.showToast({
    title: '登录成功',
    icon: 'success',
  })
}

// 登录失败处理
function handleLoginError(error: string) {
  uni.showToast({
    title: error || '登录失败',
    icon: 'error',
  })
}

// 组件挂载时加载统计数据
onMounted(() => {
  if (isLoggedIn.value) {
    loadUserStats()
  }
})

// 监听登录状态变化
watch(isLoggedIn, (newValue) => {
  if (newValue) {
    loadUserStats()
  }
  else {
    // 清空统计数据
    totalEntries.value = 0
    consecutiveDays.value = 0
    avgMood.value = '😊'
  }
})

// 加载用户统计数据
async function loadUserStats() {
  try {
    // TODO: 实际应该调用 API 获取用户统计数据
    // 这里使用模拟数据
    totalEntries.value = Math.floor(Math.random() * 100) + 1
    consecutiveDays.value = Math.floor(Math.random() * 30) + 1

    const moods = ['😊', '😃', '😌', '🙂', '😐']
    avgMood.value = moods[Math.floor(Math.random() * moods.length)]
  }
  catch (error) {
    console.error('加载用户统计数据失败:', error)
  }
}
</script>

<template>
  <view class="user-profile">
    <!-- 已登录状态 -->
    <view v-if="isLoggedIn" class="logged-in-profile">
      <!-- 用户头像和基本信息 -->
      <view class="user-info flex items-center space-x-4 rounded-xl bg-white p-4 shadow-sm">
        <view class="avatar-container relative">
          <image
            v-if="userAvatar"
            :src="userAvatar"
            class="size-16 rounded-full border-2 border-blue-100"
            mode="aspectFill"
            @error="handleAvatarError"
          />
          <view v-else class="flex size-16 items-center justify-center rounded-full bg-blue-500">
            <text class="text-xl text-white">👤</text>
          </view>

          <!-- 在线状态指示器 -->
          <view class="absolute -bottom-1 -right-1 size-5 rounded-full border-2 border-white bg-green-500" />
        </view>

        <view class="user-details flex-1">
          <text class="mb-1 block text-lg font-semibold text-gray-800">{{ userName }}</text>
          <text class="text-sm text-gray-500">{{ lastLoginText }}</text>
        </view>

        <!-- 设置按钮 -->
        <button
          class="settings-btn rounded-lg bg-gray-50 p-2 transition-colors hover:bg-gray-100"
          aria-label="用户设置"
          @click="openSettings"
        >
          <text class="text-gray-600">⚙️</text>
        </button>
      </view>

      <!-- 用户统计信息 -->
      <view class="user-stats mt-4 grid grid-cols-3 gap-3">
        <view class="stat-item rounded-lg bg-white p-3 text-center shadow-sm">
          <text class="mb-1 block text-xl font-bold text-blue-600">{{ totalEntries }}</text>
          <text class="text-xs text-gray-500">记录总数</text>
        </view>
        <view class="stat-item rounded-lg bg-white p-3 text-center shadow-sm">
          <text class="mb-1 block text-xl font-bold text-green-600">{{ consecutiveDays }}</text>
          <text class="text-xs text-gray-500">连续天数</text>
        </view>
        <view class="stat-item rounded-lg bg-white p-3 text-center shadow-sm">
          <text class="mb-1 block text-xl font-bold text-purple-600">{{ avgMood }}</text>
          <text class="text-xs text-gray-500">平均心情</text>
        </view>
      </view>

      <!-- 快捷操作 -->
      <view class="quick-actions mt-4 space-y-2">
        <button
          class="action-btn flex w-full items-center justify-between rounded-lg bg-blue-50 p-3 text-blue-700 transition-colors hover:bg-blue-100"
          @click="goToRecord"
        >
          <view class="flex items-center space-x-3">
            <text class="text-lg">📝</text>
            <text class="font-medium">记录今日情绪</text>
          </view>
          <text class="text-blue-400">›</text>
        </button>

        <button
          class="action-btn flex w-full items-center justify-between rounded-lg bg-green-50 p-3 text-green-700 transition-colors hover:bg-green-100"
          @click="goToCharts"
        >
          <view class="flex items-center space-x-3">
            <text class="text-lg">📊</text>
            <text class="font-medium">查看数据分析</text>
          </view>
          <text class="text-green-400">›</text>
        </button>
      </view>

      <!-- 退出登录按钮 -->
      <button
        class="logout-btn mt-6 w-full rounded-lg bg-red-50 p-3 font-medium text-red-600 transition-colors hover:bg-red-100"
        :disabled="isLoggingOut"
        @click="handleLogout"
      >
        {{ isLoggingOut ? '退出中...' : '退出登录' }}
      </button>
    </view>

    <!-- 未登录状态 -->
    <view v-else class="not-logged-in p-6 text-center">
      <view class="mb-6">
        <text class="mb-4 text-6xl">🔐</text>
        <text class="mb-2 block text-lg font-semibold text-gray-800">未登录</text>
        <text class="text-sm text-gray-600">请先登录以使用完整功能</text>
      </view>

      <WeChatLoginButton
        @login-success="handleLoginSuccess"
        @login-error="handleLoginError"
      />
    </view>
  </view>
</template>

<style scoped>
.user-profile {
  @apply w-full;
}

.avatar-container {
  @apply relative;
}

.settings-btn:active {
  @apply scale-95;
}

.action-btn:active {
  @apply scale-95;
}

.logout-btn:active {
  @apply scale-95;
}

.logout-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 网格布局 */
.user-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

/* 动画效果 */
.user-info,
.stat-item,
.action-btn {
  transition: all 0.2s ease;
}

.stat-item:hover {
  @apply shadow-md transform scale-105;
}
</style>
