# TabBar Icons Generation Guide

Since we cannot generate actual PNG files programmatically, here are the instructions to create the required tabbar icons:

## Required Icons

### 1. Record Icons
- **record.png** (inactive): Plus circle outline icon, 40x40px, color #888888
- **record-active.png** (active): Plus circle filled icon, 40x40px, color #8ECAE6

### 2. Charts Icons  
- **charts.png** (inactive): Bar chart outline icon, 40x40px, color #888888
- **charts-active.png** (active): Bar chart filled icon, 40x40px, color #8ECAE6

### 3. Diary Icons
- **diary.png** (inactive): Book outline icon, 40x40px, color #888888  
- **diary-active.png** (active): Book filled icon, 40x40px, color #8ECAE6

## Icon Sources

You can use these Iconify icons as reference:
- Record: `mdi:plus-circle-outline` / `mdi:plus-circle`
- Charts: `mdi:chart-bar` / `mdi:chart-line`
- Diary: `mdi:book-outline` / `mdi:book`

## Generation Methods

### Option 1: Online Icon Generators
1. Visit https://icones.js.org/
2. Search for the required icons
3. Download as PNG with the specified colors and sizes
4. Rename files according to the naming convention

### Option 2: Design Tools
1. Use Figma, Sketch, or similar tools
2. Create 40x40px artboards
3. Design simple line icons matching the style
4. Export as PNG

### Option 3: Temporary Solution
For development purposes, you can:
1. Use solid color squares as placeholders
2. Create simple text-based icons
3. Use emoji as temporary icons

## File Structure
```
src/static/tabbar/
├── record.png
├── record-active.png
├── charts.png
├── charts-active.png
├── diary.png
└── diary-active.png
```

## Note
Once the actual PNG files are created and placed in the correct directory, the tabbar will display properly in the WeChat mini-program.
