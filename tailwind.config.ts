import type { Config } from 'tailwindcss'
import { getIconCollections, iconsPlugin } from '@egoist/tailwindcss-icons'
import cssMacro from 'weapp-tailwindcss/css-macro'
import { isMp } from './platform'

export default <Config>{
  content: ['./index.html', './src/**/*.{html,js,ts,jsx,tsx,vue}'],
  theme: {
    extend: {
      colors: {
        // 今日碎语主题色板
        primary: {
          DEFAULT: '#8ECAE6', // 主色调
          light: '#A8DADC',
          dark: '#219EBC',
        },
        secondary: {
          DEFAULT: '#F4F1DE', // 辅助色
          light: '#F1FAEE',
          dark: '#E9C46A',
        },
        accent: {
          DEFAULT: '#F4A261', // 强调色
          orange: '#E76F51',
          yellow: '#FFD93D',
        },
        danger: '#FA5151', // 警告/危险色
        text: {
          primary: '#2A2A2A', // 主要文本色
          secondary: '#888888', // 次要文本色
          light: '#CCCCCC', // 浅色文本
        },
        border: {
          DEFAULT: '#EAEAEA', // 分割线
          light: '#F0F0F0',
        },
        // 情绪色相 (示例)
        emotion: {
          happy: '#FFD93D',
          calm: '#8ECAE6',
          sad: '#6C9BD1',
          anxious: '#F4A261',
          angry: '#E76F51',
          excited: '#F72585',
          tired: '#A8DADC',
          grateful: '#F1FAEE',
          confused: '#457B9D',
        },
      },
      fontFamily: {
        // 字体栈配置
        sans: [
          'PingFang SC',
          'Roboto',
          'Noto Sans',
          'system-ui',
          '-apple-system',
          'sans-serif',
        ],
        mono: [
          'DIN Alternate',
          'SF Mono',
          'Monaco',
          'Consolas',
          'monospace',
        ],
      },
      fontSize: {
        // 字体大小配置 (rpx 单位)
        'xs': ['24rpx', { lineHeight: '32rpx' }],
        'sm': ['28rpx', { lineHeight: '36rpx' }],
        'base': ['32rpx', { lineHeight: '44rpx' }],
        'lg': ['36rpx', { lineHeight: '48rpx' }],
        'xl': ['40rpx', { lineHeight: '52rpx' }],
        '2xl': ['48rpx', { lineHeight: '60rpx' }],
        '3xl': ['56rpx', { lineHeight: '68rpx' }],
      },
      spacing: {
        // 间距配置 (rpx 单位)
        '18': '36rpx',
        '22': '44rpx',
        '26': '52rpx',
        '30': '60rpx',
        '34': '68rpx',
        '38': '76rpx',
        '42': '84rpx',
        '46': '92rpx',
      },
      borderRadius: {
        // 圆角配置
        'xl': '24rpx',
        '2xl': '32rpx',
        '3xl': '40rpx',
      },
      boxShadow: {
        // 阴影配置
        'card': '0 8rpx 32rpx rgba(0, 0, 0, 0.1)',
        'fab': '0 8rpx 24rpx rgba(142, 202, 230, 0.4)',
        'modal': '0 16rpx 48rpx rgba(0, 0, 0, 0.2)',
      },
    },
  },
  // https://weapp-tw.icebreaker.top/docs/quick-start/uni-app-css-macro
  plugins: [
    cssMacro({
      variantsMap: {
        'wx': 'MP-WEIXIN',
        '-wx': {
          value: 'MP-WEIXIN',
          negative: true,
        },
        // 定义多个条件判断
        // mv: {
        //   value: 'H5 || MP-WEIXIN'
        // },
        // '-mv': {
        //   value: 'H5 || MP-WEIXIN',
        //   negative: true
        // }
      },
    }),
    iconsPlugin({
      // 在这里可以选择你要使用的 icon, 更多详见:
      // https://icon-sets.iconify.design/
      collections: getIconCollections(['svg-spinners', 'mdi', 'noto-v1']),
    }),
  ],
  corePlugins: {
    // 小程序去使用 h5 的 preflight 和响应式 container 没有意义
    preflight: !isMp,
    container: !isMp,
  },
}
